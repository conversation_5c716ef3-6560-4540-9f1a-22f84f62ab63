import os
import google.generativeai as genai
from dotenv import load_dotenv

# Load environment variables from a .env file (optional, but good practice)
load_dotenv()

# Get the API key from environment variables
# Make sure your .env file has GOOGLE_GEMINI_API_KEY="YOUR_API_KEY"
api_key = os.getenv("GOOGLE_GEMINI_API_KEY")

if not api_key:
    print("Error: GOOGLE_GEMINI_API_KEY not found in environment variables.")
    print("Please set it in your .env file or directly in the script.")
else:
    try:
        print(f"Configuring Gemini with API key: {api_key[:4]}...{api_key[-4:]}")
        genai.configure(api_key=api_key)

        # Create a GenerativeModel instance
        # You can use "gemini-1.5-flash-latest" or "gemini-pro" for text generation
        model = genai.GenerativeModel('gemini-1.5-flash-latest') # Or 'gemini-pro'

        print("Attempting to generate text with <PERSON>...")
        # Make a simple text generation request
        prompt = "What is the capital of France?"
        response = model.generate_content(prompt)

        # Print the response
        print("\nResponse from Gemini:")
        if response.candidates:
            # For gemini-1.5-flash, the text is often in parts
            generated_text = "".join(part.text for part in response.candidates[0].content.parts)
            print(generated_text)
            print("\n✅ Gemini API key seems to be working correctly!")
        else:
            print("No candidates found in the response.")
            print(f"Full response object: {response}")


    except Exception as e:
        print(f"\n❌ An error occurred: {e}")
        print("This could be due to an invalid API key, network issues, or insufficient permissions/quota.")
        print("Please double-check your API key and ensure the Generative Language API is enabled in your Google Cloud project.")

