Metadata-Version: 2.1
Name: gitdb
Version: 4.0.12
Summary: Git Object Database
Home-page: https://github.com/gitpython-developers/gitdb
Author: <PERSON>
Author-email: by<PERSON><PERSON>@gmail.com
License: BSD License
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Operating System :: POSIX
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: 3 :: Only
Requires-Python: >=3.7
License-File: LICENSE
License-File: AUTHORS
Requires-Dist: smmap<6,>=3.0.1

GitDB is a pure-Python git object database
