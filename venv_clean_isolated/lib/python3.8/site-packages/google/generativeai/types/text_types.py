# -*- coding: utf-8 -*-
# Copyright 2023 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import abc
import dataclasses
from typing import Any, Dict, Optional, List, Iterator, TypedDict

__all__ = ["Completion"]


class TextCandidate(TypedDict, total=False):
    output: str


@dataclasses.dataclass(init=False)
class Completion(abc.ABC):
    """A text completion given a prompt from the model.

    * Use `completion.candidates` to access all of the text completion options generated by the model.

    Attributes:
        candidates: A list of candidate text completions generated by the model.
    """

    candidates: List[TextCandidate]
    result: Optional[str]

    def to_dict(self) -> Dict[str, Any]:
        result = {
            "candidates": self.candidates,
        }
        return result
