# -*- coding: utf-8 -*-
# Copyright 2023 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""Utilities for processing prompts."""
from __future__ import annotations

import string
from typing import AbstractSet


def get_placeholders(prompt: str) -> AbstractSet[str]:
  """Returns the placeholders for `prompt`.

  E.g. Given "A for {word_one} B for {word_two}", returns {"word_one",
  "word_two"}.

  Args:
    prompt: A prompt template with optional placeholders.

  Returns:
    A sequence of placeholders in `prompt`.
  """
  placeholders: list[str] = []
  for _, field_name, _, _ in string.Formatter().parse(prompt):
    if field_name is not None:
      placeholders.append(field_name)
  return frozenset(placeholders)
