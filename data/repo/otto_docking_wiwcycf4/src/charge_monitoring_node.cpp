#include <ros/ros.h>
#include <actionlib/client/simple_action_client.h>
#include <actionlib/server/simple_action_server.h>
#include <actionlib/client/terminal_state.h>
#include <ottonomy_msgs/OttoDockAction.h>
#include <ottonomy_msgs/OttoDockActionGoal.h>
#include <ottonomy_msgs/RobotInfo.h>

typedef actionlib::SimpleActionClient<ottonomy_msgs::OttoDockAction> DockingActionClient;
ottonomy_msgs::OttoDockActionResult docking_client_result;
std::string abort_reson = "";

typedef actionlib::SimpleActionServer<ottonomy_msgs::OttoDockAction> OttoDockActionServer;
OttoDockActionServer *chargeAS;

double last_charge_time = 0;

void robot_info_subscriber_callback(const ottonomy_msgs::RobotInfo &robot_info)
{
    if (robot_info.status == "CHARGING" || robot_info.status == "CHARGER_CONNECTED")
    {
        last_charge_time = ros::Time::now().toSec();
    }
}

void doneCb(const actionlib::SimpleClientGoalState &state,
            const ottonomy_msgs::OttoDockResultConstPtr &result)
{
    abort_reson = result->status_message;
}

bool dock_the_robot(const ottonomy_msgs::OttoDockGoalConstPtr &goal)
{
    DockingActionClient docking_client("/otto_docking", true);
    ROS_INFO("Waiting for action servers to start.");
    docking_client.waitForServer();
    ROS_INFO("Action servers started.");

    // dock the robot
    ottonomy_msgs::OttoDockGoal docking_goal = *goal;
    docking_goal.id = std::to_string(ros::Time::now().toSec());
    docking_goal.type = "docking";

    ROS_INFO("Sending docking goal");
    docking_client.sendGoal(docking_goal, &doneCb);

    // Wait for the action to return
    bool finished_before_timeout = docking_client.waitForResult(ros::Duration(5 * 60.0));
    if (finished_before_timeout)
    {
        actionlib::SimpleClientGoalState state = docking_client.getState();
        ROS_INFO("Docking action finished with state: %s", state.toString().c_str());

        if (state != state.SUCCEEDED)
        {
            ROS_ERROR("Docking failed with state %s", state.getText().c_str());
            return false;
        }
    }
    else
    {
        ROS_ERROR("Docking action did not finish before the timeout.");
        return false;
    }

    return true;
}

bool undock_the_robot(const ottonomy_msgs::OttoDockGoalConstPtr &goal)
{
    DockingActionClient docking_client("/otto_docking", true);
    ROS_INFO("Waiting for action servers to start.");
    docking_client.waitForServer();
    ROS_INFO("Action servers started.");

    // undock the robot
    ottonomy_msgs::OttoDockGoal undocking_goal = *goal;
    undocking_goal.id = std::to_string(ros::Time::now().toSec());
    undocking_goal.type = "undocking";

    ROS_INFO("Sending undocking goal");
    docking_client.sendGoal(undocking_goal, &doneCb);

    // Wait for the action to return
    bool finished_before_timeout = docking_client.waitForResult(ros::Duration(5 * 60.0));
    if (finished_before_timeout)
    {
        actionlib::SimpleClientGoalState state = docking_client.getState();
        ROS_INFO("Undocking action finished with state: %s", state.toString().c_str());

        if (state != state.SUCCEEDED)
        {
            ROS_ERROR("Undocking failed with state %s", state.getText().c_str());
            return false;
        }
    }
    else
    {
        ROS_ERROR("Undocking action did not finish before the timeout.");
        return false;
    }

    return true;
}

void execute_cb(const ottonomy_msgs::OttoDockGoalConstPtr &goal)
{
    ROS_INFO("In execute_cb, received a new charge_monitoring goal \ngoal_type: %s \ngoal_id: %s \nstation_id: %s",
             goal->type.c_str(), goal->id.c_str(), goal->station_id.c_str());

    uint max_retries = 5;

    ros::Rate rate(10.0);
    while (ros::ok())
    {
        if (chargeAS->isPreemptRequested())
        {
            // undock the robot
            bool undocked = false;
            for (int retry_num = 0; retry_num < max_retries && !undocked; retry_num++)
                undocked = undock_the_robot(goal);

            if (!undocked)
            {
                ROS_ERROR("Rejecting goal since undocking action did not finish in %d retries", max_retries);

                ottonomy_msgs::OttoDockResult result;
                result.id = goal->id;
                result.action_completed = false;
                result.status_message = abort_reson;
                chargeAS->setAborted(result, abort_reson);

                return;
            }

            ottonomy_msgs::OttoDockResult result;
            result.id = goal->id;
            result.action_completed = false;
            result.status_message = "Undocking completed";
            chargeAS->setPreempted(result, "Undocking completed");

            return;
        }

        if (fabs(ros::Time::now().toSec() - last_charge_time) > 30.0)
        {
            bool undocked = false;
            for (int retry_num = 0; retry_num < max_retries && !undocked; retry_num++)
                undocked = undock_the_robot(goal);

            if (!undocked)
            {
                ROS_ERROR("Rejecting goal since undocking action did not finish in %d retries", max_retries);

                ottonomy_msgs::OttoDockResult result;
                result.id = goal->id;
                result.action_completed = false;
                result.status_message = abort_reson;
                chargeAS->setAborted(result, abort_reson);

                return;
            }

            bool docked = false;
            for (int retry_num = 0; retry_num < max_retries && !docked; retry_num++)
                docked = dock_the_robot(goal);

            if (!docked)
            {
                ROS_ERROR("Rejecting goal since docking action did not finish in %d retries", max_retries);

                ottonomy_msgs::OttoDockResult result;
                result.id = goal->id;
                result.action_completed = false;
                result.status_message = abort_reson;
                chargeAS->setAborted(result, abort_reson);

                return;
            }
        }

        rate.sleep();
    }
}

int main(int argc, char **argv)
{
    ros::init(argc, argv, "charge_monitoring_node");
    ros::NodeHandle nh;

    ros::Subscriber robot_info_subscriber = nh.subscribe("/ottonomy_robot_node/robot_info", 1, &robot_info_subscriber_callback);

    // create charge_monitoring action server
    chargeAS = new OttoDockActionServer(nh, "charge_monitoring", boost::bind(&execute_cb, _1), false);
    chargeAS->start();

    ros::spin();

    return 0;
}
