<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
  <BehaviorTree ID="UndockingSubtree">
    <Fallback name="UndockingRobotFallback">
      <Sequence name="UndockingRobotSequence">
        <AlwaysSuccess/>
      </Sequence>
      <Sequence name="ResetUndockingSequence">
        <AlwaysFailure/>
      </Sequence>
    </Fallback>
  </BehaviorTree>

  <!-- Description of Node Models (used by Groot) -->
  <TreeNodesModel/>

</root>
