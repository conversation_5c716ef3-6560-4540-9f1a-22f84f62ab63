<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
  <BehaviorTree ID="DockingSubtree">
    <Fallback name="DockingRootFallback">
      <Sequence name="DockingRootSequence">
        <SubTree ID="PreDockingSubtree"/>
        <RetryUntilSuccessful num_attempts="5">
          <ReactiveSequence name="MoveToLocationReactiveSequence">
            <Fallback>
              <Inverter>
                <IsPreemptRequested/>
              </Inverter>
              <ForceFailure>
                <PremptActionGoal/>
              </ForceFailure>
            </Fallback>
            <SubTree ID="MoveToLocationSubtree"/>
            <SubTree ID="MoveToContactSubtree"/>
          </ReactiveSequence>
        </RetryUntilSuccessful>
        <SubTree ID="PostDockingSubtree"/>
      </Sequence>
      <SubTree ID="ResetDockingSubtree"/>
    </Fallback>
  </BehaviorTree>

  <BehaviorTree ID="MoveToContactSubtree">
    <Fallback>
      <Sequence name="MoveToContactSequence">
        <RetryUntilSuccessful num_attempts="5">
          <Fallback>
            <DockingDriveModeStart/>
            <Delay delay_msec="2000">
              <AlwaysFailure/>
            </Delay>
          </Fallback>
        </RetryUntilSuccessful>
        <MoveToApriltagPose y_offset="0.0"
                            x_offset="0.0"
                            move_only_in_x="True"/>
        <RetryUntilSuccessful num_attempts="20">
          <Fallback>
            <StartDockCharging/>
            <Delay delay_msec="2000">
              <AlwaysFailure/>
            </Delay>
          </Fallback>
        </RetryUntilSuccessful>
        <RetryUntilSuccessful num_attempts="5">
          <Fallback>
            <DockingDriveModeStop/>
            <Delay delay_msec="2000">
              <AlwaysFailure/>
            </Delay>
          </Fallback>
        </RetryUntilSuccessful>
      </Sequence>
      <ForceFailure>
        <Sequence>
          <ForceSuccess>
            <RetryUntilSuccessful num_attempts="5">
              <Fallback>
                <DockingDriveModeStop/>
                <Delay delay_msec="2000">
                  <AlwaysFailure/>
                </Delay>
              </Fallback>
            </RetryUntilSuccessful>
          </ForceSuccess>
          <ForceSuccess>
            <RetryUntilSuccessful num_attempts="20">
              <Fallback>
                <StopDockCharging/>
                <Delay delay_msec="2000">
                  <AlwaysFailure/>
                </Delay>
              </Fallback>
            </RetryUntilSuccessful>
          </ForceSuccess>
        </Sequence>
      </ForceFailure>
    </Fallback>
  </BehaviorTree>

  <BehaviorTree ID="MoveToLocationSubtree">
    <RetryUntilSuccessful num_attempts="5">
      <Sequence>
        <SubTree ID="VirtualdockingSubtree"/>
        <MoveToApriltagPose y_offset="0.0"
                            x_offset="0.3"
                            move_only_in_x="False"/>
        <MoveToApriltagPose y_offset="0.0"
                            x_offset="0.1"
                            move_only_in_x="True"/>
      </Sequence>
    </RetryUntilSuccessful>
  </BehaviorTree>

  <BehaviorTree ID="PostDockingSubtree">
    <Sequence name="PostDockingSequence"
              _description="&gt;&gt; PostDockingSequence&#10;Turn off the apriltag detection&#10;Turn off the reduce back safety&#10;&#10;Return success if all operations success">
      <RetryUntilSuccessful num_attempts="5">
        <Fallback>
          <ApriltagDetectionStop/>
          <Delay delay_msec="2000">
            <AlwaysFailure/>
          </Delay>
        </Fallback>
      </RetryUntilSuccessful>
      <RetryUntilSuccessful num_attempts="5">
        <Fallback>
          <ReduceBackSafetyStart/>
          <Delay delay_msec="2000">
            <AlwaysFailure/>
          </Delay>
        </Fallback>
      </RetryUntilSuccessful>
    </Sequence>
  </BehaviorTree>

  <BehaviorTree ID="PreDockingSubtree">
    <Sequence name="PreDockingSequence">
      <RetryUntilSuccessful num_attempts="5">
        <Fallback>
          <ApriltagDetectionStart/>
          <Delay delay_msec="2000">
            <AlwaysFailure/>
          </Delay>
        </Fallback>
      </RetryUntilSuccessful>
      <RetryUntilSuccessful num_attempts="5">
        <Fallback>
          <ReduceBackSafetyStart/>
          <Delay delay_msec="2000">
            <AlwaysFailure/>
          </Delay>
        </Fallback>
      </RetryUntilSuccessful>
      <RetryUntilSuccessful num_attempts="20">
        <Fallback>
          <StartDockCommunication/>
          <Delay delay_msec="2000">
            <AlwaysFailure/>
          </Delay>
        </Fallback>
      </RetryUntilSuccessful>
    </Sequence>
  </BehaviorTree>

  <BehaviorTree ID="ResetDockingSubtree">
    <ForceFailure _description="&gt;&gt; ResetDockingSubtree&#10;Reset every external thing that could have been set during docking&#10;1. Turn off the apriltag&#10;2. Stop reducing back safety&#10;3. Stop docking drive mode&#10;4. Stop dock charging&#10;5. Stop dock communication">
      <Sequence>
        <ForceSuccess>
          <RetryUntilSuccessful num_attempts="5">
            <Fallback>
              <ApriltagDetectionStop/>
              <Delay delay_msec="2000">
                <AlwaysFailure/>
              </Delay>
            </Fallback>
          </RetryUntilSuccessful>
        </ForceSuccess>
        <ForceSuccess>
          <RetryUntilSuccessful num_attempts="5">
            <Fallback>
              <ReduceBackSafetyStop/>
              <Delay delay_msec="2000">
                <AlwaysFailure/>
              </Delay>
            </Fallback>
          </RetryUntilSuccessful>
        </ForceSuccess>
        <ForceSuccess>
          <RetryUntilSuccessful num_attempts="5">
            <Fallback>
              <DockingDriveModeStop/>
              <Delay delay_msec="2000">
                <AlwaysFailure/>
              </Delay>
            </Fallback>
          </RetryUntilSuccessful>
        </ForceSuccess>
        <ForceSuccess>
          <RetryUntilSuccessful num_attempts="20">
            <Fallback>
              <StopDockCharging/>
              <Delay delay_msec="5000">
                <AlwaysFailure/>
              </Delay>
            </Fallback>
          </RetryUntilSuccessful>
        </ForceSuccess>
        <ForceSuccess>
          <RetryUntilSuccessful num_attempts="20">
            <Fallback>
              <StopDockCommunication/>
              <Delay delay_msec="5000">
                <AlwaysFailure/>
              </Delay>
            </Fallback>
          </RetryUntilSuccessful>
        </ForceSuccess>
      </Sequence>
    </ForceFailure>
  </BehaviorTree>

  <!-- Description of Node Models (used by Groot) -->
  <TreeNodesModel>
    <Action ID="ApriltagDetectionStart"/>
    <Action ID="ApriltagDetectionStop"/>
    <Action ID="DockingDriveModeStart"/>
    <Action ID="DockingDriveModeStop"/>
    <Condition ID="IsPreemptRequested"/>
    <Action ID="MoveToApriltagPose">
      <input_port name="y_offset"
                  type="float"/>
      <input_port name="x_offset"
                  type="float"/>
      <input_port name="move_only_in_x"
                  type="bool"/>
    </Action>
    <Condition ID="PremptActionGoal"/>
    <Action ID="ReduceBackSafetyStart"/>
    <Action ID="ReduceBackSafetyStop"/>
    <Action ID="StartDockCharging"/>
    <Action ID="StartDockCommunication"/>
    <Action ID="StopDockCharging"/>
    <Action ID="StopDockCommunication"/>
  </TreeNodesModel>

</root>
