version: 4
environments:
  default:
    channels:
    - url: https://conda.anaconda.org/conda-forge/
    packages:
      linux-64:
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/binutils_impl_linux-64-2.40-hf600244_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-hd590300_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/c-ares-1.27.0-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ca-certificates-2024.2.2-hbcca054_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cffi-1.16.0-py311hb3a22ac_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cfgv-3.3.1-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cmake-3.28.4-hcfe8598_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/distlib-0.3.8-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/filelock-3.13.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gcc-13.2.0-hd6cf55c_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gcc_impl_linux-64-13.2.0-h338b0a0_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gmock-1.12.1-hf52228f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gtest-1.12.1-hf52228f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gxx-13.2.0-hd6cf55c_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gxx_impl_linux-64-13.2.0-h338b0a0_5.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/identify-2.5.35-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/kernel-headers_linux-64-2.6.32-he073ed8_17.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/keyutils-1.6.1-h166bdaf_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/krb5-1.21.2-h659d440_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.40-h41732ed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcurl-8.6.0-hca28451_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libedit-3.1.20191231-he28a2e2_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libev-4.33-hd590300_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.6.2-h59595ed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.2-h7f98852_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/libgcc-devel_linux-64-13.2.0-ha9c7c90_105.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-13.2.0-h807b86a_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgomp-13.2.0-h807b86a_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnghttp2-1.58.0-h47da74e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnsl-2.0.1-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsanitizer-13.2.0-h7e041cc_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsodium-1.0.18-h36c2ea0_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.40.0-h753d276_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libssh2-1.11.0-h0841786_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/libstdcxx-devel_linux-64-13.2.0-ha9c7c90_105.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-13.2.0-h7e041cc_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuv-1.48.0-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.2.13-hd590300_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/make-4.3-hd18ef5c_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.4.20240210-h59595ed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nodeenv-1.8.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.2.1-hd590300_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pre-commit-3.6.2-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pycparser-2.21-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.11.3-h2755cc3_0_cpython.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python_abi-3.11-4_cp311.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyyaml-6.0.1-py311h459d7ec_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8228510_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/rhash-1.4.4-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-69.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/sqlite-3.40.0-h4ff8645_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sysroot_linux-64-2.12-he073ed8_17.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_h4845f30_101.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2024a-h0c530f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ukkonen-1.0.1-py311h9547e67_4.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/virtualenv-20.25.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xz-5.2.6-h166bdaf_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/yaml-0.2.5-h7f98852_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zeromq-4.3.5-h59595ed_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.5-hfc55251_0.conda
      win-64:
      - conda: https://conda.anaconda.org/conda-forge/win-64/bzip2-1.0.8-hcfcfb64_5.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/ca-certificates-2024.2.2-h56e8100_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/cffi-1.16.0-py311ha68e1ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cfgv-3.3.1-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/win-64/cmake-3.28.4-hf0feee3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/distlib-0.3.8-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/filelock-3.13.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/gmock-1.12.1-h91493d7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/gtest-1.12.1-h91493d7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/identify-2.5.35-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/krb5-1.21.2-heb0366b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libcurl-8.6.0-hd5e4a3a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libexpat-2.6.2-h63175ca_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libffi-3.4.2-h8ffe710_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/win-64/libsodium-1.0.18-h8d14728_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/win-64/libsqlite-3.40.0-hcfcfb64_1.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libssh2-1.11.0-h7dfc565_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libuv-1.48.0-hcfcfb64_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libzlib-1.2.13-hcfcfb64_5.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nodeenv-1.8.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/openssl-3.2.1-hcfcfb64_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pre-commit-3.6.2-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pycparser-2.21-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/win-64/python-3.11.3-h2628c8c_0_cpython.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/python_abi-3.11-4_cp311.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/pyyaml-6.0.1-py311ha68e1ae_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-69.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/sqlite-3.40.0-hcfcfb64_1.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/tk-8.6.13-h5226925_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2024a-h0c530f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/ucrt-10.0.22621.0-h57928b3_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/win-64/ukkonen-1.0.1-py311h005e61a_4.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/vc-14.3-hcf57466_18.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/vc14_runtime-14.38.33130-h82b7239_18.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/virtualenv-20.25.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/vs2015_runtime-14.38.33130-hcb4865c_18.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/vs2022_win-64-19.38.33130-h0bfb142_18.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/vswhere-3.1.4-h57928b3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/xz-5.2.6-h8d14728_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/win-64/yaml-0.2.5-h8ffe710_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/win-64/zeromq-4.3.5-h63175ca_1.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/zstd-1.5.5-h12be248_0.conda
packages:
- kind: conda
  name: _libgcc_mutex
  version: '0.1'
  build: conda_forge
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
  sha256: fe51de6107f9edc7aa4f786a70f4a883943bc9d39b3bb7307c04c41410990726
  md5: d7c89558ba9fa0495403155b64376d81
  license: None
  size: 2562
  timestamp: 1578324546067
- kind: conda
  name: _openmp_mutex
  version: '4.5'
  build: 2_gnu
  build_number: 16
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
  sha256: fbe2c5e56a653bebb982eda4876a9178aedfc2b545f25d0ce9c4c0b508253d22
  md5: 73aaf86a425cc6e73fcf236a5a46396d
  depends:
  - _libgcc_mutex 0.1 conda_forge
  - libgomp >=7.5.0
  constrains:
  - openmp_impl 9999
  license: BSD-3-Clause
  license_family: BSD
  size: 23621
  timestamp: 1650670423406
- kind: conda
  name: binutils_impl_linux-64
  version: '2.40'
  build: hf600244_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/binutils_impl_linux-64-2.40-hf600244_0.conda
  sha256: a7e0ea2b71a5b03d82e5a58fb6b612ab1c44d72ce161f9aa441f7ba467cd4c8d
  md5: 33084421a8c0af6aef1b439707f7662a
  depends:
  - ld_impl_linux-64 2.40 h41732ed_0
  - sysroot_linux-64
  license: GPL-3.0-only
  license_family: GPL
  size: 5414922
  timestamp: 1674833958334
- kind: conda
  name: bzip2
  version: 1.0.8
  build: hcfcfb64_5
  build_number: 5
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/bzip2-1.0.8-hcfcfb64_5.conda
  sha256: ae5f47a5c86fd6db822931255dcf017eb12f60c77f07dc782ccb477f7808aab2
  md5: 26eb8ca6ea332b675e11704cce84a3be
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: bzip2-1.0.6
  license_family: BSD
  size: 124580
  timestamp: 1699280668742
- kind: conda
  name: bzip2
  version: 1.0.8
  build: hd590300_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-hd590300_5.conda
  sha256: 242c0c324507ee172c0e0dd2045814e746bb303d1eb78870d182ceb0abc726a8
  md5: 69b8b6202a07720f448be700e300ccf4
  depends:
  - libgcc-ng >=12
  license: bzip2-1.0.6
  license_family: BSD
  size: 254228
  timestamp: 1699279927352
- kind: conda
  name: c-ares
  version: 1.27.0
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/c-ares-1.27.0-hd590300_0.conda
  sha256: 2a5866b19d28cb963fab291a62ff1c884291b9d6f59de14643e52f103e255749
  md5: f6afff0e9ee08d2f1b897881a4f38cdb
  depends:
  - libgcc-ng >=12
  license: MIT
  license_family: MIT
  size: 163578
  timestamp: 1708684786032
- kind: conda
  name: ca-certificates
  version: 2024.2.2
  build: h56e8100_0
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/ca-certificates-2024.2.2-h56e8100_0.conda
  sha256: 4d587088ecccd393fec3420b64f1af4ee1a0e6897a45cfd5ef38055322cea5d0
  md5: 63da060240ab8087b60d1357051ea7d6
  license: ISC
  size: 155886
  timestamp: 1706843918052
- kind: conda
  name: ca-certificates
  version: 2024.2.2
  build: hbcca054_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/ca-certificates-2024.2.2-hbcca054_0.conda
  sha256: 91d81bfecdbb142c15066df70cc952590ae8991670198f92c66b62019b251aeb
  md5: 2f4327a1cbe7f022401b236e915a5fef
  license: ISC
  size: 155432
  timestamp: 1706843687645
- kind: conda
  name: cffi
  version: 1.16.0
  build: py311ha68e1ae_0
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/cffi-1.16.0-py311ha68e1ae_0.conda
  sha256: eb7463fe3785dd9ac0b3b1e5fea3b721d20eb082e194cab0af8d9ff28c28934f
  md5: d109d6e767c4890ea32880b8bfa4a3b6
  depends:
  - pycparser
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: MIT
  license_family: MIT
  size: 297043
  timestamp: 1696002186279
- kind: conda
  name: cffi
  version: 1.16.0
  build: py311hb3a22ac_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/cffi-1.16.0-py311hb3a22ac_0.conda
  sha256: b71c94528ca0c35133da4b7ef69b51a0b55eeee570376057f3d2ad60c3ab1444
  md5: b3469563ac5e808b0cd92810d0697043
  depends:
  - libffi >=3.4,<4.0a0
  - libgcc-ng >=12
  - pycparser
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  license: MIT
  license_family: MIT
  size: 300207
  timestamp: 1696001873452
- kind: conda
  name: cfgv
  version: 3.3.1
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/cfgv-3.3.1-pyhd8ed1ab_0.tar.bz2
  sha256: fbc03537a27ef756162c49b1d0608bf7ab12fa5e38ceb8563d6f4859e835ac5c
  md5: ebb5f5f7dc4f1a3780ef7ea7738db08c
  depends:
  - python >=3.6.1
  license: MIT
  license_family: MIT
  size: 10788
  timestamp: 1629909423398
- kind: conda
  name: cmake
  version: 3.28.4
  build: hcfe8598_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/cmake-3.28.4-hcfe8598_0.conda
  sha256: 6c7d7552e0ce3b142d0f18de64b6d9f1eccc8825aa3b7127a4000e539d73a9f7
  md5: 8f3da5e9e920897a86ce0abece94935b
  depends:
  - bzip2 >=1.0.8,<2.0a0
  - libcurl >=8.6.0,<9.0a0
  - libexpat >=2.6.2,<3.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libuv >=1.48.0,<2.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - ncurses >=6.4.20240210,<7.0a0
  - rhash >=1.4.4,<2.0a0
  - xz >=5.2.6,<6.0a0
  - zstd >=1.5.5,<1.6.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 18695647
  timestamp: 1710960841751
- kind: conda
  name: cmake
  version: 3.28.4
  build: hf0feee3_0
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/cmake-3.28.4-hf0feee3_0.conda
  sha256: 793677299ffd26ea6fcd142a52136e5dc15b30059c8240d29c3dc95467923fe8
  md5: 53ae1dbb7b800e771b5182e3c641d207
  depends:
  - bzip2 >=1.0.8,<2.0a0
  - libcurl >=8.6.0,<9.0a0
  - libexpat >=2.6.2,<3.0a0
  - libuv >=1.48.0,<2.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - ucrt >=10.0.20348.0
  - vc14_runtime >=14.29.30139
  - xz >=5.2.6,<6.0a0
  - zstd >=1.5.5,<1.6.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 13871194
  timestamp: 1710961869326
- kind: conda
  name: distlib
  version: 0.3.8
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/distlib-0.3.8-pyhd8ed1ab_0.conda
  sha256: 3ff11acdd5cc2f80227682966916e878e45ced94f59c402efb94911a5774e84e
  md5: db16c66b759a64dc5183d69cc3745a52
  depends:
  - python 2.7|>=3.6
  license: Apache-2.0
  license_family: APACHE
  size: 274915
  timestamp: 1702383349284
- kind: conda
  name: filelock
  version: 3.13.1
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/filelock-3.13.1-pyhd8ed1ab_0.conda
  sha256: 4d742d91412d1f163e5399d2b50c5d479694ebcd309127abb549ca3977f89d2b
  md5: 0c1729b74a8152fde6a38ba0a2ab9f45
  depends:
  - python >=3.7
  license: Unlicense
  size: 15605
  timestamp: 1698715139726
- kind: conda
  name: gcc
  version: 13.2.0
  build: hd6cf55c_3
  build_number: 3
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/gcc-13.2.0-hd6cf55c_3.conda
  sha256: 7438ff57cf37cca306db8b70d25b6eb144bc70339dd375afac8beb3a3b6495f5
  md5: 78ece817e46368937ea2827b8b625eca
  depends:
  - gcc_impl_linux-64 13.2.0.*
  license: BSD-3-Clause
  license_family: BSD
  size: 27439
  timestamp: 1710259879706
- kind: conda
  name: gcc_impl_linux-64
  version: 13.2.0
  build: h338b0a0_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/gcc_impl_linux-64-13.2.0-h338b0a0_5.conda
  sha256: baab8f8b9af54959735e629cf6d5ec9378166aa4c68ba8dc98dc0a781d548409
  md5: a6be13181cb66a78544b1d5f7bac97d0
  depends:
  - binutils_impl_linux-64 >=2.39
  - libgcc-devel_linux-64 13.2.0 ha9c7c90_105
  - libgcc-ng >=13.2.0
  - libgomp >=13.2.0
  - libsanitizer 13.2.0 h7e041cc_5
  - libstdcxx-ng >=13.2.0
  - sysroot_linux-64
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 53318565
  timestamp: 1706819323755
- kind: conda
  name: gmock
  version: 1.12.1
  build: h91493d7_0
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/gmock-1.12.1-h91493d7_0.conda
  sha256: d73f5769f81bee19b2328ac1a8a70d7a4029f354b952daf5982691e17e0500d1
  md5: fb3cd0c6db137c027c069928d407a80d
  depends:
  - gtest 1.12.1 h91493d7_0
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vs2015_runtime >=14.29.30139
  license: BSD-3-Clause
  license_family: BSD
  size: 5479845
  timestamp: 1674025431241
- kind: conda
  name: gmock
  version: 1.12.1
  build: hf52228f_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/gmock-1.12.1-hf52228f_0.conda
  sha256: 3b0e8020bf8dfe48fa41b796131ce085fab5a33d0bc9e3337d15be986d062be1
  md5: 7be98dcbb1648d0bebf57848ed13d50c
  depends:
  - gtest 1.12.1 hf52228f_0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 143428
  timestamp: 1674024011073
- kind: conda
  name: gtest
  version: 1.12.1
  build: h91493d7_0
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/gtest-1.12.1-h91493d7_0.conda
  sha256: cf724e19be93231a192126bc7f0adc3ab4fa87e357f9bb02a339cbf057efbc5e
  md5: 11e1eadb67bd706007093c98bbbabb67
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vs2015_runtime >=14.29.30139
  constrains:
  - gmock 1.12.1
  license: BSD-3-Clause
  license_family: BSD
  size: 3491342
  timestamp: 1674025366743
- kind: conda
  name: gtest
  version: 1.12.1
  build: hf52228f_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/gtest-1.12.1-hf52228f_0.conda
  sha256: 0cbcf769905a7bd782f2cc277154df6207fcdb678be9eb2232ec81bd8ff65eeb
  md5: d4c0791266a18618786961ef67817cbc
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  constrains:
  - gmock 1.12.1
  license: BSD-3-Clause
  license_family: BSD
  size: 284354
  timestamp: 1674023994746
- kind: conda
  name: gxx
  version: 13.2.0
  build: hd6cf55c_3
  build_number: 3
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/gxx-13.2.0-hd6cf55c_3.conda
  sha256: 433ea239bca69f64c4262d4d660f7511a925b7a2819d096554c9788e35d46371
  md5: 8988c1eaea17d0cec6af9da7b6241e3b
  depends:
  - gcc 13.2.0.*
  - gxx_impl_linux-64 13.2.0.*
  license: BSD-3-Clause
  license_family: BSD
  size: 26970
  timestamp: 1710260263832
- kind: conda
  name: gxx_impl_linux-64
  version: 13.2.0
  build: h338b0a0_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/gxx_impl_linux-64-13.2.0-h338b0a0_5.conda
  sha256: 9049d84fef7526e1dde8311acd2a592bf1d6f16453e68087c17d1bda01eb7867
  md5: 88d0ccab114eb0e837725bd48cdddae5
  depends:
  - gcc_impl_linux-64 13.2.0 h338b0a0_5
  - libstdcxx-devel_linux-64 13.2.0 ha9c7c90_105
  - sysroot_linux-64
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 13582212
  timestamp: 1706819574801
- kind: conda
  name: identify
  version: 2.5.35
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/identify-2.5.35-pyhd8ed1ab_0.conda
  sha256: 971683b13d1b820157bef9993c63dd8b0611d2d60fc4b522da163aee2e70e518
  md5: 9472bfd206a2b7bb8143835e37667054
  depends:
  - python >=3.6
  - ukkonen
  license: MIT
  license_family: MIT
  size: 78364
  timestamp: 1708283690891
- kind: conda
  name: kernel-headers_linux-64
  version: 2.6.32
  build: he073ed8_17
  build_number: 17
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/kernel-headers_linux-64-2.6.32-he073ed8_17.conda
  sha256: fb39d64b48f3d9d1acc3df208911a41f25b6a00bd54935d5973b4739a9edd5b6
  md5: d731b543793afc0433c4fd593e693fce
  constrains:
  - sysroot_linux-64 ==2.12
  license: LGPL-2.0-or-later AND LGPL-2.0-or-later WITH exceptions AND GPL-2.0-or-later AND MPL-2.0
  license_family: GPL
  size: 710627
  timestamp: 1708000830116
- kind: conda
  name: keyutils
  version: 1.6.1
  build: h166bdaf_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/keyutils-1.6.1-h166bdaf_0.tar.bz2
  sha256: 150c05a6e538610ca7c43beb3a40d65c90537497a4f6a5f4d15ec0451b6f5ebb
  md5: 30186d27e2c9fa62b45fb1476b7200e3
  depends:
  - libgcc-ng >=10.3.0
  license: LGPL-2.1-or-later
  size: 117831
  timestamp: 1646151697040
- kind: conda
  name: krb5
  version: 1.21.2
  build: h659d440_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/krb5-1.21.2-h659d440_0.conda
  sha256: 259bfaae731989b252b7d2228c1330ef91b641c9d68ff87dae02cbae682cb3e4
  md5: cd95826dbd331ed1be26bdf401432844
  depends:
  - keyutils >=1.6.1,<2.0a0
  - libedit >=3.1.20191231,<3.2.0a0
  - libedit >=3.1.20191231,<4.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - openssl >=3.1.2,<4.0a0
  license: MIT
  license_family: MIT
  size: 1371181
  timestamp: 1692097755782
- kind: conda
  name: krb5
  version: 1.21.2
  build: heb0366b_0
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/krb5-1.21.2-heb0366b_0.conda
  sha256: 6002adff9e3dcfc9732b861730cb9e33d45fd76b2035b2cdb4e6daacb8262c0b
  md5: 6e8b0f22b4eef3b3cb3849bb4c3d47f9
  depends:
  - openssl >=3.1.2,<4.0a0
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: MIT
  license_family: MIT
  size: 710894
  timestamp: 1692098129546
- kind: conda
  name: ld_impl_linux-64
  version: '2.40'
  build: h41732ed_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.40-h41732ed_0.conda
  sha256: f6cc89d887555912d6c61b295d398cff9ec982a3417d38025c45d5dd9b9e79cd
  md5: 7aca3059a1729aa76c597603f10b0dd3
  constrains:
  - binutils_impl_linux-64 2.40
  license: GPL-3.0-only
  license_family: GPL
  size: 704696
  timestamp: 1674833944779
- kind: conda
  name: libcurl
  version: 8.6.0
  build: hca28451_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libcurl-8.6.0-hca28451_0.conda
  sha256: 357ce806adf1818dc8dccdcd64627758e1858eb0d8a9c91aae4a0eeee2a44608
  md5: 704739398d858872cb91610f49f0ef29
  depends:
  - krb5 >=1.21.2,<1.22.0a0
  - libgcc-ng >=12
  - libnghttp2 >=1.58.0,<2.0a0
  - libssh2 >=1.11.0,<2.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - openssl >=3.2.1,<4.0a0
  - zstd >=1.5.5,<1.6.0a0
  license: curl
  license_family: MIT
  size: 391187
  timestamp: 1710590979402
- kind: conda
  name: libcurl
  version: 8.6.0
  build: hd5e4a3a_0
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/libcurl-8.6.0-hd5e4a3a_0.conda
  sha256: 49904a3c1ede193cf9044e8379067bf56850fb03f64abbf57ca45f7e6d2d3888
  md5: 9cc8dea844a89245dfe8618521ef8d6a
  depends:
  - krb5 >=1.21.2,<1.22.0a0
  - libssh2 >=1.11.0,<2.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: curl
  license_family: MIT
  size: 325841
  timestamp: 1710591351093
- kind: conda
  name: libedit
  version: 3.1.20191231
  build: he28a2e2_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libedit-3.1.20191231-he28a2e2_2.tar.bz2
  sha256: a57d37c236d8f7c886e01656f4949d9dcca131d2a0728609c6f7fa338b65f1cf
  md5: 4d331e44109e3f0e19b4cb8f9b82f3e1
  depends:
  - libgcc-ng >=7.5.0
  - ncurses >=6.2,<7.0.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 123878
  timestamp: 1597616541093
- kind: conda
  name: libev
  version: '4.33'
  build: hd590300_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libev-4.33-hd590300_2.conda
  sha256: 1cd6048169fa0395af74ed5d8f1716e22c19a81a8a36f934c110ca3ad4dd27b4
  md5: 172bf1cd1ff8629f2b1179945ed45055
  depends:
  - libgcc-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  size: 112766
  timestamp: 1702146165126
- kind: conda
  name: libexpat
  version: 2.6.2
  build: h59595ed_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.6.2-h59595ed_0.conda
  sha256: 331bb7c7c05025343ebd79f86ae612b9e1e74d2687b8f3179faec234f986ce19
  md5: e7ba12deb7020dd080c6c70e7b6f6a3d
  depends:
  - libgcc-ng >=12
  constrains:
  - expat 2.6.2.*
  license: MIT
  license_family: MIT
  size: 73730
  timestamp: 1710362120304
- kind: conda
  name: libexpat
  version: 2.6.2
  build: h63175ca_0
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/libexpat-2.6.2-h63175ca_0.conda
  sha256: 79f612f75108f3e16bbdc127d4885bb74729cf66a8702fca0373dad89d40c4b7
  md5: bc592d03f62779511d392c175dcece64
  constrains:
  - expat 2.6.2.*
  license: MIT
  license_family: MIT
  size: 139224
  timestamp: 1710362609641
- kind: conda
  name: libffi
  version: 3.4.2
  build: h7f98852_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.2-h7f98852_5.tar.bz2
  sha256: ab6e9856c21709b7b517e940ae7028ae0737546122f83c2aa5d692860c3b149e
  md5: d645c6d2ac96843a2bfaccd2d62b3ac3
  depends:
  - libgcc-ng >=9.4.0
  license: MIT
  license_family: MIT
  size: 58292
  timestamp: 1636488182923
- kind: conda
  name: libffi
  version: 3.4.2
  build: h8ffe710_5
  build_number: 5
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/libffi-3.4.2-h8ffe710_5.tar.bz2
  sha256: 1951ab740f80660e9bc07d2ed3aefb874d78c107264fd810f24a1a6211d4b1a5
  md5: 2c96d1b6915b408893f9472569dee135
  depends:
  - vc >=14.1,<15.0a0
  - vs2015_runtime >=14.16.27012
  license: MIT
  license_family: MIT
  size: 42063
  timestamp: 1636489106777
- kind: conda
  name: libgcc-devel_linux-64
  version: 13.2.0
  build: ha9c7c90_105
  build_number: 105
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/libgcc-devel_linux-64-13.2.0-ha9c7c90_105.conda
  sha256: 858029ad4d66869c533bb5a22e95e7c044ca66c61d6f403f10d9ae074a0e360e
  md5: 3bc29a967fee57e193ce51f51c598bca
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 2578210
  timestamp: 1706819085946
- kind: conda
  name: libgcc-ng
  version: 13.2.0
  build: h807b86a_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-13.2.0-h807b86a_5.conda
  sha256: d32f78bfaac282cfe5205f46d558704ad737b8dbf71f9227788a5ca80facaba4
  md5: d4ff227c46917d3b4565302a2bbb276b
  depends:
  - _libgcc_mutex 0.1 conda_forge
  - _openmp_mutex >=4.5
  constrains:
  - libgomp 13.2.0 h807b86a_5
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 770506
  timestamp: 1706819192021
- kind: conda
  name: libgomp
  version: 13.2.0
  build: h807b86a_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libgomp-13.2.0-h807b86a_5.conda
  sha256: 0d3d4b1b0134283ea02d58e8eb5accf3655464cf7159abf098cc694002f8d34e
  md5: d211c42b9ce49aee3734fdc828731689
  depends:
  - _libgcc_mutex 0.1 conda_forge
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 419751
  timestamp: 1706819107383
- kind: conda
  name: libnghttp2
  version: 1.58.0
  build: h47da74e_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libnghttp2-1.58.0-h47da74e_1.conda
  sha256: 1910c5306c6aa5bcbd623c3c930c440e9c77a5a019008e1487810e3c1d3716cb
  md5: 700ac6ea6d53d5510591c4344d5c989a
  depends:
  - c-ares >=1.23.0,<2.0a0
  - libev >=4.33,<4.34.0a0
  - libev >=4.33,<5.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  - openssl >=3.2.0,<4.0a0
  license: MIT
  license_family: MIT
  size: 631936
  timestamp: 1702130036271
- kind: conda
  name: libnsl
  version: 2.0.1
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libnsl-2.0.1-hd590300_0.conda
  sha256: 26d77a3bb4dceeedc2a41bd688564fe71bf2d149fdcf117049970bc02ff1add6
  md5: 30fd6e37fe21f86f4bd26d6ee73eeec7
  depends:
  - libgcc-ng >=12
  license: LGPL-2.1-only
  license_family: GPL
  size: 33408
  timestamp: 1697359010159
- kind: conda
  name: libsanitizer
  version: 13.2.0
  build: h7e041cc_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libsanitizer-13.2.0-h7e041cc_5.conda
  sha256: 97ecdab7e4e96400d712c2d6ba2b7c30a97278e9f4470ea0ff36bf4f1447b3b9
  md5: 3f686300a92604d1bdff9a29dd4a6639
  depends:
  - libgcc-ng >=13.2.0
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 4114208
  timestamp: 1706819228913
- kind: conda
  name: libsodium
  version: 1.0.18
  build: h36c2ea0_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libsodium-1.0.18-h36c2ea0_1.tar.bz2
  sha256: 53da0c8b79659df7b53eebdb80783503ce72fb4b10ed6e9e05cc0e9e4207a130
  md5: c3788462a6fbddafdb413a9f9053e58d
  depends:
  - libgcc-ng >=7.5.0
  license: ISC
  size: 374999
  timestamp: 1605135674116
- kind: conda
  name: libsodium
  version: 1.0.18
  build: h8d14728_1
  build_number: 1
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/libsodium-1.0.18-h8d14728_1.tar.bz2
  sha256: ecc463f0ab6eaf6bc5bd6ff9c17f65595de6c7a38db812222ab8ffde0d3f4bc2
  md5: 5c1fb45b5e2912c19098750ae8a32604
  depends:
  - vc >=14.1,<15.0a0
  - vs2015_runtime >=14.16.27012
  license: ISC
  size: 713431
  timestamp: 1605135918736
- kind: conda
  name: libsqlite
  version: 3.40.0
  build: h753d276_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.40.0-h753d276_1.conda
  sha256: 15df20516c936d7a7f0d4cfb8207e5b35e7cc49cf89894752bb4caa3e9b48dcc
  md5: 32565f92ca100184c67509d1d91c858a
  depends:
  - libgcc-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  license: Unlicense
  size: 804156
  timestamp: 1682197905901
- kind: conda
  name: libsqlite
  version: 3.40.0
  build: hcfcfb64_1
  build_number: 1
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/libsqlite-3.40.0-hcfcfb64_1.conda
  sha256: 9af4e83408533854fe716e5cea1eafc576ac93684ee641ddd7ca8cc2e3b0baf0
  md5: 2832b5d2aa943a96b5046cfab01d2462
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vs2015_runtime >=14.29.30139
  license: Unlicense
  size: 825318
  timestamp: 1682198555134
- kind: conda
  name: libssh2
  version: 1.11.0
  build: h0841786_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libssh2-1.11.0-h0841786_0.conda
  sha256: 50e47fd9c4f7bf841a11647ae7486f65220cfc988ec422a4475fe8d5a823824d
  md5: 1f5a58e686b13bcfde88b93f547d23fe
  depends:
  - libgcc-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  - openssl >=3.1.1,<4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 271133
  timestamp: 1685837707056
- kind: conda
  name: libssh2
  version: 1.11.0
  build: h7dfc565_0
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/libssh2-1.11.0-h7dfc565_0.conda
  sha256: 813fd04eed2a2d5d9c36e53c554f9c1f08e9324e2922bd60c9c52dbbed2dbcec
  md5: dc262d03aae04fe26825062879141a41
  depends:
  - libzlib >=1.2.13,<1.3.0a0
  - openssl >=3.1.1,<4.0a0
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: BSD-3-Clause
  license_family: BSD
  size: 266806
  timestamp: 1685838242099
- kind: conda
  name: libstdcxx-devel_linux-64
  version: 13.2.0
  build: ha9c7c90_105
  build_number: 105
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/libstdcxx-devel_linux-64-13.2.0-ha9c7c90_105.conda
  sha256: 67e999ee56481844ca4ce2e61132c5c16f3f00a05daa1d0ea4b2c684eea5de5a
  md5: 66383205c2e1bdf013df52fa9e3e6763
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 13020920
  timestamp: 1706819128553
- kind: conda
  name: libstdcxx-ng
  version: 13.2.0
  build: h7e041cc_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-13.2.0-h7e041cc_5.conda
  sha256: a56c5b11f1e73a86e120e6141a42d9e935a99a2098491ac9e15347a1476ce777
  md5: f6f6600d18a4047b54f803cf708b868a
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 3834139
  timestamp: 1706819252496
- kind: conda
  name: libuuid
  version: 2.38.1
  build: h0b41bf4_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
  sha256: 787eb542f055a2b3de553614b25f09eefb0a0931b0c87dbcce6efdfd92f04f18
  md5: 40b61aab5c7ba9ff276c41cfffe6b80b
  depends:
  - libgcc-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 33601
  timestamp: 1680112270483
- kind: conda
  name: libuv
  version: 1.48.0
  build: hcfcfb64_0
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/libuv-1.48.0-hcfcfb64_0.conda
  sha256: 6151c51857c2407139ce22fdc956022353e675b2bc96991a9201d51cceaa90b4
  md5: 485e49e1d500d996844df14cabf64d73
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: MIT
  license_family: MIT
  size: 289753
  timestamp: 1709913743184
- kind: conda
  name: libuv
  version: 1.48.0
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libuv-1.48.0-hd590300_0.conda
  sha256: b7c0e8a0c93c2621be7645b37123d4e8d27e8a974da26a3fba47a9c37711aa7f
  md5: 7e8b914b1062dd4386e3de4d82a3ead6
  depends:
  - libgcc-ng >=12
  license: MIT
  license_family: MIT
  size: 899979
  timestamp: 1709913354710
- kind: conda
  name: libzlib
  version: 1.2.13
  build: hcfcfb64_5
  build_number: 5
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/libzlib-1.2.13-hcfcfb64_5.conda
  sha256: c161822ee8130b71e08b6d282b9919c1de2c5274b29921a867bca0f7d30cad26
  md5: 5fdb9c6a113b6b6cb5e517fd972d5f41
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  constrains:
  - zlib 1.2.13 *_5
  license: Zlib
  license_family: Other
  size: 55800
  timestamp: 1686575452215
- kind: conda
  name: libzlib
  version: 1.2.13
  build: hd590300_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.2.13-hd590300_5.conda
  sha256: 370c7c5893b737596fd6ca0d9190c9715d89d888b8c88537ae1ef168c25e82e4
  md5: f36c115f1ee199da648e0597ec2047ad
  depends:
  - libgcc-ng >=12
  constrains:
  - zlib 1.2.13 *_5
  license: Zlib
  license_family: Other
  size: 61588
  timestamp: 1686575217516
- kind: conda
  name: make
  version: '4.3'
  build: hd18ef5c_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/make-4.3-hd18ef5c_1.tar.bz2
  sha256: 4a5fe7c80bb0de0015328e2d3fc8db1736f528cb1fd53cd0d5527e24269a4f7c
  md5: 4049ebfd3190b580dffe76daed26155a
  depends:
  - libgcc-ng >=7.5.0
  license: GPL-3.0-or-later
  license_family: GPL
  size: 518896
  timestamp: 1602706451788
- kind: conda
  name: ncurses
  version: 6.4.20240210
  build: h59595ed_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.4.20240210-h59595ed_0.conda
  sha256: aa0f005b6727aac6507317ed490f0904430584fa8ca722657e7f0fb94741de81
  md5: 97da8860a0da5413c7c98a3b3838a645
  depends:
  - libgcc-ng >=12
  license: X11 AND BSD-3-Clause
  size: 895669
  timestamp: 1710866638986
- kind: conda
  name: nodeenv
  version: 1.8.0
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/nodeenv-1.8.0-pyhd8ed1ab_0.conda
  sha256: 1320306234552717149f36f825ddc7e27ea295f24829e9db4cc6ceaff0b032bd
  md5: 2a75b296096adabbabadd5e9782e5fcc
  depends:
  - python 2.7|>=3.7
  - setuptools
  license: BSD-3-Clause
  license_family: BSD
  size: 34358
  timestamp: 1683893151613
- kind: conda
  name: openssl
  version: 3.2.1
  build: hcfcfb64_1
  build_number: 1
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/openssl-3.2.1-hcfcfb64_1.conda
  sha256: 61ce4e11c3c26ed4e4d9b7e7e2483121a1741ad0f9c8db0a91a28b6e05182ce6
  md5: 958e0418e93e50c575bff70fbcaa12d8
  depends:
  - ca-certificates
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  constrains:
  - pyopenssl >=22.1
  license: Apache-2.0
  license_family: Apache
  size: 8230112
  timestamp: 1710796158475
- kind: conda
  name: openssl
  version: 3.2.1
  build: hd590300_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.2.1-hd590300_1.conda
  sha256: 2c689444ed19a603be457284cf2115ee728a3fafb7527326e96054dee7cdc1a7
  md5: 9d731343cff6ee2e5a25c4a091bf8e2a
  depends:
  - ca-certificates
  - libgcc-ng >=12
  constrains:
  - pyopenssl >=22.1
  license: Apache-2.0
  license_family: Apache
  size: 2865379
  timestamp: 1710793235846
- kind: conda
  name: platformdirs
  version: 4.2.0
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.2.0-pyhd8ed1ab_0.conda
  sha256: 2ebfb971236ab825dd79dd6086ea742a9901008ffb9c6222c1f2b5172a8039d3
  md5: a0bc3eec34b0fab84be6b2da94e98e20
  depends:
  - python >=3.8
  license: MIT
  license_family: MIT
  size: 20210
  timestamp: 1706713564353
- kind: conda
  name: pre-commit
  version: 3.6.2
  build: pyha770c72_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/pre-commit-3.6.2-pyha770c72_0.conda
  sha256: 8eb9f5965c37d2bbee9302e16cc7c5517ee06491986356112be13431a043681e
  md5: 61534ee57ffdf26d7b1b514d33daccc4
  depends:
  - cfgv >=2.0.0
  - identify >=1.0.0
  - nodeenv >=0.11.1
  - python >=3.9
  - pyyaml >=5.1
  - virtualenv >=20.10.0
  license: MIT
  license_family: MIT
  size: 179884
  timestamp: 1708284490635
- kind: conda
  name: pycparser
  version: '2.21'
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/pycparser-2.21-pyhd8ed1ab_0.tar.bz2
  sha256: 74c63fd03f1f1ea2b54e8bc529fd1a600aaafb24027b738d0db87909ee3a33dc
  md5: 076becd9e05608f8dc72757d5f3a91ff
  depends:
  - python ==2.7.*|>=3.4
  license: BSD-3-Clause
  license_family: BSD
  size: 102747
  timestamp: 1636257201998
- kind: conda
  name: python
  version: 3.11.3
  build: h2628c8c_0_cpython
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/python-3.11.3-h2628c8c_0_cpython.conda
  sha256: 092b7bc17a0065b82f512d56910c521fd665e8020536014f1a9e3f2a47b448bb
  md5: 8f82e0e0ba51bed311f28eb6450393f6
  depends:
  - bzip2 >=1.0.8,<2.0a0
  - libexpat >=2.5.0,<3.0a0
  - libffi >=3.4,<4.0a0
  - libsqlite >=3.40.0,<4.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - openssl >=3.1.0,<4.0a0
  - tk >=8.6.12,<8.7.0a0
  - tzdata
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vs2015_runtime >=14.29.30139
  - xz >=5.2.6,<6.0a0
  constrains:
  - python_abi 3.11.* *_cp311
  license: Python-2.0
  size: 18128687
  timestamp: 1680771386664
- kind: conda
  name: python
  version: 3.11.3
  build: h2755cc3_0_cpython
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/python-3.11.3-h2755cc3_0_cpython.conda
  sha256: 5013c65e922895baa6f60fba4ec84ee13b9a53e1fb9aa66804c33f74ea236024
  md5: 37005ea5f68df6a8a381b70cf4d4a160
  depends:
  - bzip2 >=1.0.8,<2.0a0
  - ld_impl_linux-64 >=2.36.1
  - libexpat >=2.5.0,<3.0a0
  - libffi >=3.4,<4.0a0
  - libgcc-ng >=12
  - libnsl >=2.0.0,<2.1.0a0
  - libsqlite >=3.40.0,<4.0a0
  - libuuid >=2.38.1,<3.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - ncurses >=6.3,<7.0a0
  - openssl >=3.1.0,<4.0a0
  - readline >=8.2,<9.0a0
  - tk >=8.6.12,<8.7.0a0
  - tzdata
  - xz >=5.2.6,<6.0a0
  constrains:
  - python_abi 3.11.* *_cp311
  license: Python-2.0
  size: 31074906
  timestamp: 1680772780604
- kind: conda
  name: python_abi
  version: '3.11'
  build: 4_cp311
  build_number: 4
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/python_abi-3.11-4_cp311.conda
  sha256: 0be3ac1bf852d64f553220c7e6457e9c047dfb7412da9d22fbaa67e60858b3cf
  md5: d786502c97404c94d7d58d258a445a65
  constrains:
  - python 3.11.* *_cpython
  license: BSD-3-Clause
  license_family: BSD
  size: 6385
  timestamp: 1695147338551
- kind: conda
  name: python_abi
  version: '3.11'
  build: 4_cp311
  build_number: 4
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/python_abi-3.11-4_cp311.conda
  sha256: 67c2aade3e2160642eec0742384e766b20c766055e3d99335681e3e05d88ed7b
  md5: 70513332c71b56eace4ee6441e66c012
  constrains:
  - python 3.11.* *_cpython
  license: BSD-3-Clause
  license_family: BSD
  size: 6755
  timestamp: 1695147711935
- kind: conda
  name: pyyaml
  version: 6.0.1
  build: py311h459d7ec_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/pyyaml-6.0.1-py311h459d7ec_1.conda
  sha256: 28729ef1ffa7f6f9dfd54345a47c7faac5d34296d66a2b9891fb147f4efe1348
  md5: 52719a74ad130de8fb5d047dc91f247a
  depends:
  - libgcc-ng >=12
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - yaml >=0.2.5,<0.3.0a0
  license: MIT
  license_family: MIT
  size: 200626
  timestamp: 1695373818537
- kind: conda
  name: pyyaml
  version: 6.0.1
  build: py311ha68e1ae_1
  build_number: 1
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/pyyaml-6.0.1-py311ha68e1ae_1.conda
  sha256: 4fb0770fc70381a8ab3ced33413ad9dc5e82d4c535b593edd580113ce8760298
  md5: 2b4128962cd665153e946f2a88667a3b
  depends:
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  - yaml >=0.2.5,<0.3.0a0
  license: MIT
  license_family: MIT
  size: 175469
  timestamp: 1695374086205
- kind: conda
  name: readline
  version: '8.2'
  build: h8228510_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8228510_1.conda
  sha256: 5435cf39d039387fbdc977b0a762357ea909a7694d9528ab40f005e9208744d7
  md5: 47d31b792659ce70f470b5c82fdfb7a4
  depends:
  - libgcc-ng >=12
  - ncurses >=6.3,<7.0a0
  license: GPL-3.0-only
  license_family: GPL
  size: 281456
  timestamp: 1679532220005
- kind: conda
  name: rhash
  version: 1.4.4
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/rhash-1.4.4-hd590300_0.conda
  sha256: 12711d2d4a808a503c2e49b25d26ecb351435521e814c154e682dd2be71c2611
  md5: ec972a9a2925ac8d7a19eb9606561fff
  depends:
  - libgcc-ng >=12
  license: MIT
  license_family: MIT
  size: 185144
  timestamp: 1693455923632
- kind: conda
  name: setuptools
  version: 69.2.0
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/setuptools-69.2.0-pyhd8ed1ab_0.conda
  sha256: 78a75c75a5dacda6de5f4056c9c990141bdaf4f64245673a590594d00bc63713
  md5: da214ecd521a720a9d521c68047682dc
  depends:
  - python >=3.8
  license: MIT
  license_family: MIT
  size: 471183
  timestamp: 1710344615844
- kind: conda
  name: sqlite
  version: 3.40.0
  build: h4ff8645_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/sqlite-3.40.0-h4ff8645_1.conda
  sha256: 282b2aae20e00144281d483ea598d4a42479c27258790a9a23b26506beef67a6
  md5: a5cec05bd9e0a5843fa29de9591b93cb
  depends:
  - libgcc-ng >=12
  - libsqlite 3.40.0 h753d276_1
  - libzlib >=1.2.13,<1.3.0a0
  - ncurses >=6.3,<7.0a0
  - readline >=8.2,<9.0a0
  license: Unlicense
  size: 791390
  timestamp: 1682197916132
- kind: conda
  name: sqlite
  version: 3.40.0
  build: hcfcfb64_1
  build_number: 1
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/sqlite-3.40.0-hcfcfb64_1.conda
  sha256: ebc8950334e2680a765887772bb56a1694b1c1ebcd402cea4c2d780822b79875
  md5: 5eef83db4f6a19d3c4112076822a73e5
  depends:
  - libsqlite 3.40.0 hcfcfb64_1
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vs2015_runtime >=14.29.30139
  license: Unlicense
  size: 822571
  timestamp: 1682198584526
- kind: conda
  name: sysroot_linux-64
  version: '2.12'
  build: he073ed8_17
  build_number: 17
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/sysroot_linux-64-2.12-he073ed8_17.conda
  sha256: b4e4d685e41cb36cfb16f0cb15d2c61f8f94f56fab38987a44eff95d8a673fb5
  md5: 595db67e32b276298ff3d94d07d47fbf
  depends:
  - kernel-headers_linux-64 2.6.32 he073ed8_17
  license: LGPL-2.0-or-later AND LGPL-2.0-or-later WITH exceptions AND GPL-2.0-or-later AND MPL-2.0
  license_family: GPL
  size: 15127123
  timestamp: 1708000843849
- kind: conda
  name: tk
  version: 8.6.13
  build: h5226925_1
  build_number: 1
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/tk-8.6.13-h5226925_1.conda
  sha256: 2c4e914f521ccb2718946645108c9bd3fc3216ba69aea20c2c3cedbd8db32bb1
  md5: fc048363eb8f03cd1737600a5d08aafe
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: TCL
  license_family: BSD
  size: 3503410
  timestamp: 1699202577803
- kind: conda
  name: tk
  version: 8.6.13
  build: noxft_h4845f30_101
  build_number: 101
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_h4845f30_101.conda
  sha256: e0569c9caa68bf476bead1bed3d79650bb080b532c64a4af7d8ca286c08dea4e
  md5: d453b98d9c83e71da0741bb0ff4d76bc
  depends:
  - libgcc-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  license: TCL
  license_family: BSD
  size: 3318875
  timestamp: 1699202167581
- kind: conda
  name: tzdata
  version: 2024a
  build: h0c530f3_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/tzdata-2024a-h0c530f3_0.conda
  sha256: 7b2b69c54ec62a243eb6fba2391b5e443421608c3ae5dbff938ad33ca8db5122
  md5: 161081fc7cec0bfda0d86d7cb595f8d8
  license: LicenseRef-Public-Domain
  size: 119815
  timestamp: 1706886945727
- kind: conda
  name: ucrt
  version: 10.0.22621.0
  build: h57928b3_0
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/ucrt-10.0.22621.0-h57928b3_0.tar.bz2
  sha256: f29cdaf8712008f6b419b8b1a403923b00ab2504bfe0fb2ba8eb60e72d4f14c6
  md5: 72608f6cd3e5898229c3ea16deb1ac43
  constrains:
  - vs2015_runtime >=14.29.30037
  license: LicenseRef-Proprietary
  license_family: PROPRIETARY
  size: 1283972
  timestamp: 1666630199266
- kind: conda
  name: ukkonen
  version: 1.0.1
  build: py311h005e61a_4
  build_number: 4
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/ukkonen-1.0.1-py311h005e61a_4.conda
  sha256: ef774047df25201a6425fe1ec194505a3cac9ba02e96953360442f59364d12b3
  md5: d9988836cc20c90e05901ab05962f496
  depends:
  - cffi
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: MIT
  license_family: MIT
  size: 17225
  timestamp: 1695549858085
- kind: conda
  name: ukkonen
  version: 1.0.1
  build: py311h9547e67_4
  build_number: 4
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/ukkonen-1.0.1-py311h9547e67_4.conda
  sha256: c2d33e998f637b594632eba3727529171a06eb09896e36aa42f1ebcb03779472
  md5: 586da7df03b68640de14dc3e8bcbf76f
  depends:
  - cffi
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  license: MIT
  license_family: MIT
  size: 13961
  timestamp: 1695549513130
- kind: conda
  name: vc
  version: '14.3'
  build: hcf57466_18
  build_number: 18
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/vc-14.3-hcf57466_18.conda
  sha256: 447a8d8292a7b2107dcc18afb67f046824711a652725fc0f522c368e7a7b8318
  md5: 20e1e652a4c740fa719002a8449994a2
  depends:
  - vc14_runtime >=14.38.33130
  track_features:
  - vc14
  license: BSD-3-Clause
  license_family: BSD
  size: 16977
  timestamp: 1702511255313
- kind: conda
  name: vc14_runtime
  version: 14.38.33130
  build: h82b7239_18
  build_number: 18
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/vc14_runtime-14.38.33130-h82b7239_18.conda
  sha256: bf94c9af4b2e9cba88207001197e695934eadc96a5c5e4cd7597e950aae3d8ff
  md5: 8be79fdd2725ddf7bbf8a27a4c1f79ba
  depends:
  - ucrt >=10.0.20348.0
  constrains:
  - vs2015_runtime 14.38.33130.* *_18
  license: LicenseRef-ProprietaryMicrosoft
  license_family: Proprietary
  size: 749868
  timestamp: 1702511239004
- kind: conda
  name: virtualenv
  version: 20.25.1
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/virtualenv-20.25.1-pyhd8ed1ab_0.conda
  sha256: 1ced4445cf72cd9dc344ad04bdaf703a08cc428c8c46e4bda928ad79786ee153
  md5: 8797a4e26be36880a603aba29c785352
  depends:
  - distlib <1,>=0.3.7
  - filelock <4,>=3.12.2
  - platformdirs <5,>=3.9.1
  - python >=3.8
  license: MIT
  license_family: MIT
  size: 3148218
  timestamp: 1708602229963
- kind: conda
  name: vs2015_runtime
  version: 14.38.33130
  build: hcb4865c_18
  build_number: 18
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/vs2015_runtime-14.38.33130-hcb4865c_18.conda
  sha256: a2fec221f361d6263c117f4ea6d772b21c90a2f8edc6f3eb0eadec6bfe8843db
  md5: 10d42885e3ed84e575b454db30f1aa93
  depends:
  - vc14_runtime >=14.38.33130
  license: BSD-3-Clause
  license_family: BSD
  size: 16988
  timestamp: 1702511261442
- kind: conda
  name: vs2022_win-64
  version: 19.38.33130
  build: h0bfb142_18
  build_number: 18
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/vs2022_win-64-19.38.33130-h0bfb142_18.conda
  sha256: 9b7107dcc5c4100c610d03f773dfc57b8fe4d92363dc8fa6b0c1e2a7887c1d1d
  md5: b04b8f57f9c40f74329d277a8dcf68e7
  depends:
  - vswhere
  track_features:
  - vc14
  license: BSD-3-Clause
  license_family: BSD
  size: 19529
  timestamp: 1702511248981
- kind: conda
  name: vswhere
  version: 3.1.4
  build: h57928b3_0
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/vswhere-3.1.4-h57928b3_0.conda
  sha256: 553c41fc1a883415a39444313f8d99236685529776fdd04e8d97288b73496002
  md5: b1d1d6a1f874d8c93a57b5efece52f03
  license: MIT
  license_family: MIT
  size: 218421
  timestamp: 1682376911339
- kind: conda
  name: xz
  version: 5.2.6
  build: h166bdaf_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xz-5.2.6-h166bdaf_0.tar.bz2
  sha256: 03a6d28ded42af8a347345f82f3eebdd6807a08526d47899a42d62d319609162
  md5: 2161070d867d1b1204ea749c8eec4ef0
  depends:
  - libgcc-ng >=12
  license: LGPL-2.1 and GPL-2.0
  size: 418368
  timestamp: 1660346797927
- kind: conda
  name: xz
  version: 5.2.6
  build: h8d14728_0
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/xz-5.2.6-h8d14728_0.tar.bz2
  sha256: 54d9778f75a02723784dc63aff4126ff6e6749ba21d11a6d03c1f4775f269fe0
  md5: 515d77642eaa3639413c6b1bc3f94219
  depends:
  - vc >=14.1,<15
  - vs2015_runtime >=14.16.27033
  license: LGPL-2.1 and GPL-2.0
  size: 217804
  timestamp: 1660346976440
- kind: conda
  name: yaml
  version: 0.2.5
  build: h7f98852_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/yaml-0.2.5-h7f98852_2.tar.bz2
  sha256: a4e34c710eeb26945bdbdaba82d3d74f60a78f54a874ec10d373811a5d217535
  md5: 4cb3ad778ec2d5a7acbdf254eb1c42ae
  depends:
  - libgcc-ng >=9.4.0
  license: MIT
  license_family: MIT
  size: 89141
  timestamp: 1641346969816
- kind: conda
  name: yaml
  version: 0.2.5
  build: h8ffe710_2
  build_number: 2
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/yaml-0.2.5-h8ffe710_2.tar.bz2
  sha256: 4e2246383003acbad9682c7c63178e2e715ad0eb84f03a8df1fbfba455dfedc5
  md5: adbfb9f45d1004a26763652246a33764
  depends:
  - vc >=14.1,<15.0a0
  - vs2015_runtime >=14.16.27012
  license: MIT
  license_family: MIT
  size: 63274
  timestamp: 1641347623319
- kind: conda
  name: zeromq
  version: 4.3.5
  build: h59595ed_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/zeromq-4.3.5-h59595ed_1.conda
  sha256: 3bec658f5c23abf5e200d98418add7a20ff7b45c928ad4560525bef899496256
  md5: 7fc9d3288d2420bb3637647621018000
  depends:
  - libgcc-ng >=12
  - libsodium >=1.0.18,<1.0.19.0a0
  - libstdcxx-ng >=12
  license: MPL-2.0
  license_family: MOZILLA
  size: 343438
  timestamp: 1709135220800
- kind: conda
  name: zeromq
  version: 4.3.5
  build: h63175ca_1
  build_number: 1
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/zeromq-4.3.5-h63175ca_1.conda
  sha256: c9089e80a724a4d21f9df4bcc99ccbddb93c8cce3f6b0c9cb74b4f98b641dfc2
  md5: e8867cc4d023f41f54bd64a33436b0a1
  depends:
  - libsodium >=1.0.18,<1.0.19.0a0
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: MPL-2.0
  license_family: MOZILLA
  size: 4199151
  timestamp: 1709135717106
- kind: conda
  name: zstd
  version: 1.5.5
  build: h12be248_0
  subdir: win-64
  url: https://conda.anaconda.org/conda-forge/win-64/zstd-1.5.5-h12be248_0.conda
  sha256: d540dd56c5ec772b60e4ce7d45f67f01c6614942225885911964ea1e70bb99e3
  md5: 792bb5da68bf0a6cac6a6072ecb8dbeb
  depends:
  - libzlib >=1.2.13,<1.3.0a0
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: BSD-3-Clause
  license_family: BSD
  size: 343428
  timestamp: 1693151615801
- kind: conda
  name: zstd
  version: 1.5.5
  build: hfc55251_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.5-hfc55251_0.conda
  sha256: 607cbeb1a533be98ba96cf5cdf0ddbb101c78019f1fda063261871dad6248609
  md5: 04b88013080254850d6c01ed54810589
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 545199
  timestamp: 1693151163452
