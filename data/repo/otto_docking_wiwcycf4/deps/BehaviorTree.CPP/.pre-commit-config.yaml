
# To use:
#
#     pre-commit run -a
#
# Or:
#
#     pre-commit install  # (runs every time you commit in git)
#
# To update this file:
#
#     pre-commit autoupdate
#
# See https://github.com/pre-commit/pre-commit

exclude: ^3rdparty/|3rdparty|^include/behaviortree_cpp/contrib/
repos:

  # Standard hooks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: check-added-large-files
      - id: check-ast
      - id: check-case-conflict
      - id: check-docstring-first
      - id: check-merge-conflict
      - id: check-symlinks
      - id: check-xml
      - id: check-yaml
      - id: debug-statements
      - id: end-of-file-fixer
        exclude_types: [svg]
      - id: mixed-line-ending
      - id: trailing-whitespace
        exclude_types: [svg]
      - id: fix-byte-order-marker

  # CPP hooks
  - repo: https://github.com/pre-commit/mirrors-clang-format
    rev: v17.0.6
    hooks:
      - id: clang-format
        args: ['-fallback-style=none', '-i']
