#!/usr/bin/env python

PACKAGE = 'otto_docking' 
import sys
from dynamic_reconfigure.parameter_generator_catkin import ParameterGenerator, double_t, int_t, bool_t, str_t

gen = ParameterGenerator()

gen.add("camera_frame", str_t, 0, "camera frame", "bottom_front")
gen.add("docking_distance", double_t, 0, "distance at which robot needs to dock", 1.0, 0.5, 5.0)
gen.add("undocking_distance", double_t, 0, "distance at which robot needs to undock", 2.0, 0.5, 5.0)
gen.add("error_threshold", double_t, 0, "maximum offset the robot can have in both x and y axis", 0.03, 0.5, 1.0)
gen.add("error_threshold_yaw", double_t, 0, "maximum offset the robot can have in yaw ", 0.05, 0.02, 1.0)
gen.add("docking_retries", int_t, 0, "number of attempts allowed for docking ", 5, 1, 20)
gen.add("max_linear_velocity_limit", double_t, 0, "Maximum linear velocity of the robot for docking ", 0.2, 0.05,  1.0)
gen.add("min_linear_velocity_limit", double_t, 0, "Minimum linear velocity of the robot for docking ", 0.06, 0.05, 1.0)
gen.add("max_angular_velocity_limit", double_t, 0, "Maximum Angular velocity of the robot for docking ", 0.2, 0.05, 1.0)
gen.add("min_angular_velocity_limit", double_t, 0, "Minimum Angular velocity of the robot for docking ", 0.05, 0.05, 1.0)
gen.add("kp_x", double_t, 0, "Gain value in x axis", 0.4, 0.05, 1.0)
gen.add("kp_y", double_t, 0, "Gain value in y axis", 0.4, 0.05, 1.0)
gen.add("kp_yaw", double_t, 0, "Gain value in yaw axis",0.4, 0.05,  1.0)
gen.add("use_sinh_law", bool_t, 0, "Use Sinh control law for docking", True)
gen.add("use_tanh_law", bool_t, 0, "Use Tanh control law for docking", False)
gen.add("enable_backwards_docking", bool_t, 0, "enable docking via back camera", False)



exit(gen.generate(PACKAGE, "otto_docking", "OttoDocking"))