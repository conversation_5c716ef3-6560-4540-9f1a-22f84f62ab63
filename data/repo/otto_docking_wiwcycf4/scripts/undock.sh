rostopic pub --once /otto_docking/goal ottonomy_msgs/OttoDockActionGoal "header:
  seq: 0
  stamp:
    secs: 0
    nsecs: 0
  frame_id: 'map'
goal_id:
  stamp:
    secs: 0
    nsecs: 0
  id: '$(date +%s)'
goal:
  header:
    seq: 0
    stamp: {secs: 0, nsecs: 0}
    frame_id: 'map'
  id: '$(date +%s)'
  type: 'undocking'
  station_id: '1'
  target_pose:
    header:
      seq: 0
      stamp: {secs: 0, nsecs: 0}
      frame_id: 'map'
    pose:
      position: {x: 2.820, y: 11.393, z: 0.0}
      orientation: {x: -0.001, y: -0.001, z: 0.588, w: 0.809}"
