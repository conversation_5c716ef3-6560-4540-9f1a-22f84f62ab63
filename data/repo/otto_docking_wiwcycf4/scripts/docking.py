#!/usr/bin/env python3

import time
import rospy
from apriltag_ros.msg import AprilTagDetectionArray
from std_srvs.srv import SetBool, SetBoolResponse
from nav_msgs.msg import Odometry
from geometry_msgs.msg import PoseStamped, Twist, Quaternion, Point
from move_base_msgs.msg import MoveBaseAction, MoveBaseGoal
import tf2_ros
import tf2_geometry_msgs
import geometry_msgs.msg
import tf.transformations as tf_trans
from tf.transformations import euler_from_quaternion, quaternion_multiply, quaternion_about_axis, quaternion_from_euler, quaternion_matrix, quaternion_from_matrix
import math
from visualization_msgs.msg import Marker
import numpy as np
import csv

class OttoDocking:
    def __init__(self):
        rospy.init_node('target_pose_update', anonymous=False)
        self.tf_buffer = tf2_ros.Buffer(rospy.Duration(100.0))
        self.tf_listener = tf2_ros.TransformListener(self.tf_buffer)
        # robot pose 
        self.robot_pose_subscriber = rospy.Subscriber('/ekf_map', Odometry, self.robot_pose_callback)
        # target pose
        self.target_pose_subscriber = rospy.Subscriber('/tag_detections', AprilTagDetectionArray, self.detection_callback)
        #docking
        self.docking_trigger_service = rospy.Service('/start_docking', SetBool, self.start_docking_callback)
        self.undocking_trigger_service = rospy.Service('/start_undocking', SetBool, self.start_undocking_callback)

        #cmd_vel
        self.cmd_vel_pub = rospy.Publisher('/cmd_vel', Twist, queue_size=10)
        self.start_docking = False
        self.marker_pose = PoseStamped()
        self.marker_in_base = PoseStamped()
        self.projected_pose = PoseStamped()
        self.odom_in_base = PoseStamped()
        self.global_odom_pose = PoseStamped()
        self.docking_pose_in_base = PoseStamped()
        self.translated_pose = PoseStamped()
        self.translated_pose_final = PoseStamped()
        self.translated_pose_in_base = PoseStamped()
        self.translated_pose_final_in_base = PoseStamped()

        self.rate = rospy.Rate(10)
        self.backwards_docking = True
        # P gains for x,y and yaw
        self.kp_x = 0.4#for Sinh 0.2 and max velocit 0.2   #for tanh 0.4 max velocity 0.2
        self.kp_y = 0.4
        self.kp_yaw = 0.4
        # error_threshold for x,y and yaw
        self.error_threshold = 0.03
        self.error_threshold_yaw = 0.03
        #max x, y, angular velocity limits
        self.max_linear_velocity_limit = 0.2
        self.max_angular_velocity_limit = 0.2
        #min x, y, angular velocity limits
        self.min_linear_velocity_limit_x = 0.06
        self.min_linear_velocity_limit_y = 0.02
        self.min_angular_velocity_limit = 0.05

        self.find_target = False
        self.target_found = True
        self.docking_state = False
        self.docking_stage = 0
        self.distance_in_front = 1.0
        self.target_detected = False
        self.use_sinh_law = True
        self.use_tanh_law = False


        self.undocking_stage = 0
        self.undocking_pose = PoseStamped()
        self.translated_pose_undock = PoseStamped()
        self.undocking_state = False

	# callback function of service /auto_docking
    def start_docking_callback(self, auto_docking):
        # docking_state = rospy.get_param('docking')
        if(not self.docking_state):
            self.docking_state = auto_docking
            self.docking_stage = 1
            return SetBoolResponse(success=True, message="Docking request recieved.....")
        

	# callback function of service /auto_undocking
    def start_undocking_callback(self, auto_undocking):
        # docking_state = rospy.get_param('docking')
        if(not self.undocking_state):
            self.undocking_state = auto_docking
            self.undocking_stage = 1
            return SetBoolResponse(success=True, message="UnDocking request recieved.....")

    def translation_and_orientation_correction(self, input_pose, distance_in_front):

        translation_vector = [0.0, 0.0, distance_in_front]  # Translation vector in x direction

        # Convert quaternion to rotation matrix
        rotation_matrix = quaternion_matrix([input_pose.pose.orientation.x,
                                            input_pose.pose.orientation.y,
                                            input_pose.pose.orientation.z,
                                            input_pose.pose.orientation.w])

        # Calculate the direction of translation in the local coordinate system
        translated_position_local = np.dot(rotation_matrix[:3, :3], translation_vector)

        # Apply translation
        translated_position = np.array([input_pose.pose.position.x + translated_position_local[0],
                                        input_pose.pose.position.y + translated_position_local[1],
                                        input_pose.pose.position.z + translated_position_local[2]])

            # Convert back to PoseStamped
        projected_pose = PoseStamped()
        projected_pose.header.frame_id = input_pose.header.frame_id
        projected_pose.pose.position.x = translated_position[0]
        projected_pose.pose.position.y = translated_position[1]
        projected_pose.pose.position.z = translated_position[2]
        projected_pose.pose.orientation = input_pose.pose.orientation

        # Convert quaternion to Euler angles
        euler_angles = euler_from_quaternion([projected_pose.pose.orientation.x,
                                            projected_pose.pose.orientation.y,
                                            projected_pose.pose.orientation.z,
                                            projected_pose.pose.orientation.w])

        # Add 90 degrees to yaw angle (rotation about Z-axis)
        yaw_angle = euler_angles[2] - np.pi / 2  # Add 90 degrees in radians

        # Convert Euler angles back to quaternion
        quat_corrected_orientation = quaternion_from_euler(euler_angles[0], yaw_angle, euler_angles[1])

        # Update the orientation of the projected pose
        projected_pose.pose.orientation.x = quat_corrected_orientation[0]
        projected_pose.pose.orientation.y = quat_corrected_orientation[1]
        projected_pose.pose.orientation.z = quat_corrected_orientation[2]
        projected_pose.pose.orientation.w = quat_corrected_orientation[3]

        return projected_pose

    def create_line_pose(self, pose1, pose2):
        # Extract position from PoseStamped messages
        position1 = pose1.pose.position
        position2 = pose2.pose.position

        # Convert positions to NumPy arrays
        pose1_array = np.array([position1.x, 0.0, position1.z])
        pose2_array = np.array([position2.x, 0.0, position2.z])

        # Calculate LineVector
        line_vector = pose2_array - pose1_array

        # Calculate Midpoint
        midpoint_array = (pose1_array + pose2_array) / 2

        # Calculate Perpendicular Vector
    
        up_vector = np.array([0, -1, 0])
        perpendicular_vector = np.cross(line_vector, up_vector)

        # Normalize Perpendicular Vector
        perpendicular_vector /= np.linalg.norm(perpendicular_vector)

        # Calculate Roll, Pitch, and Yaw angles based on the perpendicular vector
        roll = 0  # Assuming no roll
        pitch = np.arcsin(-perpendicular_vector[1])
        yaw = np.arctan2(perpendicular_vector[2], perpendicular_vector[0])


        # Apply 180-degree rotation if backwards_docking is enabled
        if not self.backwards_docking:
            _, _, current_yaw = euler_from_quaternion(quaternion_from_euler(0, pitch, yaw))
            yaw += np.pi  # Add 180 degrees to the yaw angle

        # Translate the midpoint 1 meter forward
        translated_point = midpoint_array + 1.0 * perpendicular_vector
        translated_point_2 = midpoint_array + 1.2 * perpendicular_vector
        translated_point_3 = midpoint_array + 0.86 * perpendicular_vector
        translated_point_4 = midpoint_array + 2.0 * perpendicular_vector #undocking 

        # Create a new PoseStamped for the projected point
        projected_pose = PoseStamped()
        # Assuming the headers are the same
        # projected_pose.header = pose1.header
        projected_pose.pose.position = Point(*translated_point)

        # Set orientation based on roll, pitch, and yaw
        orientation_quaternion = quaternion_from_euler(roll, -yaw, pitch)
        projected_pose.pose.orientation.x = orientation_quaternion[0]
        projected_pose.pose.orientation.y = orientation_quaternion[1]
        projected_pose.pose.orientation.z = orientation_quaternion[2]
        projected_pose.pose.orientation.w = orientation_quaternion[3]
        
        self.translated_pose.pose.position = Point(*translated_point_2)
        self.translated_pose.pose.orientation = projected_pose.pose.orientation

        self.translated_pose_undock.pose.position = Point(*translated_point_4)
        self.translated_pose_undock.pose.orientation = projected_pose.pose.orientation

        self.translated_pose_final.pose.position = Point(*translated_point_3)
        self.translated_pose_final.pose.orientation = projected_pose.pose.orientation
        return projected_pose

    def detection_callback(self, marker):
        if len(marker.detections) != 2:
            rospy.loginfo_throttle_identical(3, "Need exactly 2 April Tags for docking!")
            self.find_target = True
            self.target_detected = False
            return
        
        # Extract the poses of the two detected April tags
        pose_tag1 = marker.detections[0].pose.pose
        pose_tag2 = marker.detections[1].pose.pose

        # Create a pose representing the center of the line joining the two tags
        line_pose = PoseStamped()
        line_pose = self.create_line_pose(pose_tag1, pose_tag2)
        line_pose.header = marker.header
        self.translated_pose.header = line_pose.header
        self.translated_pose_undock.header = line_pose.header
        self.translated_pose_final.header = line_pose.header
        
        # Project a new docking pose in front of the line
        distance_in_front = 1.0  # Replace with your desired value for the distance

        # Transform the docking pose to the base_link_nav frame /realsense_mid_front_color_optical_frame
        transform = self.tf_buffer.lookup_transform('base_link_nav', 'bottom_back', rospy.Time(0))
        self.docking_pose_in_base = tf2_geometry_msgs.do_transform_pose(line_pose, transform)

        #transformu = self.tf_buffer.lookup_transform('base_link_nav', 'bottom_front', rospy.Time(0))
        self.translated_pose_in_base = tf2_geometry_msgs.do_transform_pose(self.translated_pose, transform)
        self.translated_pose_final_in_base = tf2_geometry_msgs.do_transform_pose(self.translated_pose_final, transform)

        self.undocking_pose = tf2_geometry_msgs.do_transform_pose(self.translated_pose_undock, transform)


        # Use the docking pose as needed
        self.publish_marker_point(self.docking_pose_in_base, "docking", [1.0, 1.0, 0.0, 1.0], 0.1)

        # Calculate and log the error in x, y, and yaw
        error_x = self.docking_pose_in_base.pose.position.x - self.marker_in_base.pose.position.x
        error_y = self.docking_pose_in_base.pose.position.y - self.marker_in_base.pose.position.y
        error_yaw = self.calculate_yaw_error(self.docking_pose_in_base.pose)
        # rospy.loginfo("Error in x: %f, Error in y: %f, Error in yaw: %f", error_x, error_y, error_yaw)

        self.target_detected = True
        self.find_target = False

    def robot_pose_callback(self, odom_msg):
        odom_to_base_tf = self.tf_buffer.lookup_transform('base_link_nav', 'odom', rospy.Time(0))
        odom_pose = PoseStamped()
        odom_pose.header = odom_msg.header
        odom_pose.pose = odom_msg.pose.pose
        # self.odom_in_base = odom_pose
        self.odom_in_base = tf2_geometry_msgs.do_transform_pose(odom_pose, odom_to_base_tf)
        # rospy.loginfo("Odom_yaw: %f", self.calculate_yaw_error(self.odom_in_base.pose))
        self.publish_marker_point(odom_pose, "odom", [0.0, 0.0, 1.0, 1.0], 0.1)
        self.global_odom_pose = odom_pose

    def calculate_yaw_error(self, pose1):
        quaternion1 = [pose1.orientation.x, pose1.orientation.y, pose1.orientation.z, pose1.orientation.w]
        # Convert quaternions to Euler angles
        euler_angles1 = euler_from_quaternion(quaternion1)
        yaw = euler_angles1[2]
        return yaw
    
    def publish_marker_point(self, pose_stamped, namespace, color, size):
        marker = Marker()
        marker.header = pose_stamped.header
        marker.ns = namespace
        marker.id = 0
        marker.type = Marker.ARROW  # Use ARROW type for visualization of orientation
        marker.action = Marker.ADD
        marker.pose = pose_stamped.pose
        marker.scale.x = size * 2.0  # Adjust the scale for the arrow length
        marker.scale.y = size / 2.0  # Adjust the scale for the arrow width
        marker.scale.z = size / 2.0  # Adjust the scale for the arrow height
        marker.color.r = color[0]
        marker.color.g = color[1]
        marker.color.b = color[2]
        marker.color.a = color[3]
        marker.lifetime = rospy.Duration(1)  # Adjust the lifetime as needed

        # Use a Publisher to publish the marker
        marker_pub = rospy.Publisher('/visualization_marker', Marker, queue_size=10)
        marker_pub.publish(marker)

    def rotate_quaternion_by_180_degrees(self, quaternion):
        # Rotate the quaternion by 180 degrees around the z-axis
        rotation_quaternion = quaternion_about_axis(math.pi, [0, 0, 1])
        rotated_quaternion = quaternion_multiply(quaternion, rotation_quaternion)
        return rotated_quaternion



    def calc_valid_velocities(self, V_x, V_y, V_yaw):
        straight_steering_when_stopping = True
        steer_max_cmd = 1800
        position_scaling_factor = 4864
        max_steer_angle = steer_max_cmd * math.pi / position_scaling_factor
        ROBOT_LENGTH = 0.685
        ROBOT_WIDTH = 0.425
        FRONT_OFFSET_DISTANCE = 0.070
        Rx_in = ROBOT_LENGTH / 2.0
        Ry_in = ROBOT_WIDTH / 2.0
        OD_in = FRONT_OFFSET_DISTANCE

        vel_pos = [0.0] * 8
        S_FL, V_FL, S_FR, V_FR, S_RL, V_RL, S_RR, V_RR = 0, 1, 2, 3, 4, 5, 6, 7

        Vx_in = V_x
        Vy_in = V_y
        if V_x < 0.0:
            V_yaw *= -1
            Vy_in *= -1
        Vx, Vy, Rx, Ry = 0.0, 0.0, 0.0, 0.0
        dir = int(Vx_in / abs(Vx_in))
        Vx = dir * Vx_in - V_yaw * Ry_in
        Vy = Vy_in + V_yaw * Rx_in
        vel_pos[S_FL] = math.atan2(Vy, Vx)
        Rx = Rx_in - math.sin(vel_pos[S_FL]) * OD_in
        Ry = Ry_in + math.cos(vel_pos[S_FL]) * OD_in
        Vx = dir * Vx_in - V_yaw * Ry
        Vy = Vy_in + V_yaw * Rx
        vel_pos[V_FL] = dir * math.sqrt(Vy * Vy + Vx * Vx)

        Vx = dir * Vx_in + V_yaw * Ry_in
        Vy = Vy_in + V_yaw * Rx_in
        vel_pos[S_FR] = math.atan2(Vy, Vx)
        vel_pos[V_FR] = math.sqrt(Vy * Vy + Vx * Vx)
        Rx = Rx_in - math.sin(vel_pos[S_FR]) * -OD_in
        Ry = Ry_in + math.cos(vel_pos[S_FR]) * OD_in
        Vx = dir * Vx_in + V_yaw * Ry
        Vy = Vy_in + V_yaw * Rx
        vel_pos[V_FR] = dir * math.sqrt(Vy * Vy + Vx * Vx)

        Vx = dir * Vx_in - V_yaw * Ry_in
        Vy = Vy_in - V_yaw * Rx_in
        vel_pos[S_RL] = math.atan2(Vy, Vx)
        Rx = Rx_in - math.sin(vel_pos[S_RL]) * -OD_in
        Ry = Ry_in + math.cos(vel_pos[S_RL]) * OD_in
        Vx = dir * Vx_in - V_yaw * Ry
        Vy = Vy_in - V_yaw * Rx
        vel_pos[V_RL] = dir * math.sqrt(Vy * Vy + Vx * Vx)

        Vx = dir * Vx_in + V_yaw * Ry_in
        Vy = Vy_in - V_yaw * Rx_in
        vel_pos[S_RR] = math.atan2(Vy, Vx)
        Rx = Rx_in - math.sin(vel_pos[S_RR]) * OD_in
        Ry = Ry_in + math.cos(vel_pos[S_RR]) * OD_in
        Vx = dir * Vx_in + V_yaw * Ry
        Vy = Vy_in - V_yaw * Rx
        vel_pos[V_RR] = dir * math.sqrt(Vy * Vy + Vx * Vx)

        if all(abs(angle) < max_steer_angle for angle in vel_pos[::2]):  # Consider only steering angles
            prev_valid_swerve_vel_pos = vel_pos
            rospy.loginfo("Got Valid swerve commands")
            # return V_x, V_y, V_yaw
            return True
        else:
            print("Invalid input commands, swerve steering command out of range")
            rospy.logerr("Got Invalid swerve commands !!")
            return False

    def dock(self):
        sleep = False
        while not rospy.is_shutdown():
            if self.undocking_state:
                cmd_vel = Twist()
                if self.backwards_docking:
                    backwards_docking_factor = -1
                else:
                    backwards_docking_factor = 1

                error_x = self.undocking_pose.pose.position.x * backwards_docking_factor
                error_y = self.undocking_pose.pose.position.y
                error_yaw = self.calculate_yaw_error(self.undocking_pose.pose)
                rospy.loginfo("error_x: %f error_y: %f error_yaw: %f", error_x, error_y, error_yaw)
                if self.undocking_stage == 1:
                    if abs(error_x) >= self.error_threshold:
                        if self.use_sinh_law:
                            # Use hyperbolic sine-based control law V = Vmax*Sinh(K * Error /Vmax)
                            cmd_vel.linear.x = self.max_linear_velocity_limit * math.sinh(self.kp_x * error_x / self.max_linear_velocity_limit) * backwards_docking_factor
                            cmd_vel.linear.x = max(min(cmd_vel.linear.x, self.max_linear_velocity_limit), -self.max_linear_velocity_limit)
                        elif self.use_tanh_law:
                            # Use hyperbolic tangent-based control law
                            cmd_vel.linear.x = self.max_linear_velocity_limit * math.tanh(self.kp_x * error_x / self.max_linear_velocity_limit) * backwards_docking_factor
                            cmd_vel.linear.x = max(min(cmd_vel.linear.x, self.max_linear_velocity_limit), -self.max_linear_velocity_limit)
                        else:
                            cmd_vel.linear.x = max(min(self.kp_x * error_x, self.max_linear_velocity_limit), -self.max_linear_velocity_limit) * backwards_docking_factor
                        # cmd_vel.linear.y = max(min(self.kp_y * error_y, self.max_linear_velocity_limit), -self.max_linear_velocity_limit)

                        # Ensure the linear velocity is within the specified limits
                        if cmd_vel.linear.x < 0.0:
                            cmd_vel.linear.x = min(cmd_vel.linear.x, -self.min_linear_velocity_limit_x)
                        elif cmd_vel.linear.x > 0.0:
                            cmd_vel.linear.x = max(cmd_vel.linear.x, self.min_linear_velocity_limit_x)
                        else:
                            cmd_vel.linear.x = 0.0

                        rospy.logwarn_throttle_identical(1,"undocking_stage == 1 cmd_vel.linear.x: %f", cmd_vel.linear.x)
                        rospy.logwarn_throttle_identical(1,"undocking_stage == 1 cmd_vel.linear.y: %f", cmd_vel.linear.y)
                        rospy.logwarn_throttle_identical(1,"undocking_stage == 1 cmd_vel.angular.z: %f", cmd_vel.angular.z)
                    else:
                        rospy.loginfo("Robot Undocked")
                        cmd_vel.linear.x = 0.1
                        cmd_vel.linear.y = 0.0
                        cmd_vel.linear.z = 0.0
                        cmd_vel.angular.z = 0.0
                        self.undocking_state = False
                        self.undocking_stage = 2
                        rospy.loginfo("error_x: %f error_y: %f error_yaw: %f", error_x, error_y, error_yaw)
                self.cmd_vel_pub.publish(cmd_vel)

            if self.docking_state and not self.target_detected and self.docking_stage !=3:
                rospy.logerr("Looking up the target .....")
                cmd_vel = Twist()
                cmd_vel.angular.z = 0.1
                self.cmd_vel_pub.publish(cmd_vel)
                continue
            if self.docking_state and self.target_detected:
                cmd_vel = Twist()
                projected_pose = PoseStamped()
                if self.docking_stage == 1:
                    projected_pose = self.translated_pose_in_base
                elif self.docking_stage == 2:
                    projected_pose = self.docking_pose_in_base
                elif self.docking_stage == 3:
                    projected_pose = self.translated_pose_final_in_base

                self.publish_marker_point(projected_pose, "docking_marker", [1.0, 0.0, 0.0, 1.0], 0.1)
                if self.backwards_docking:
                    backwards_docking_factor = -1
                else:
                    backwards_docking_factor = 1

                error_x = projected_pose.pose.position.x * backwards_docking_factor
                error_y = projected_pose.pose.position.y
                error_yaw = self.calculate_yaw_error(projected_pose.pose)

                rospy.loginfo_throttle_identical(1,"error_x: %f", error_x)
                rospy.loginfo_throttle_identical(1,"error_y: %f", error_y)
                # rospy.logwarn_throttle_identical(1,"error_yaw: %f", error_yaw)
                # self.rule_based = True
                if self.docking_stage == 4:
                    if abs(error_yaw) >= self.error_threshold_yaw:
                        if self.use_sinh_law:
                            cmd_vel.angular.z = self.max_angular_velocity_limit * math.sinh(self.kp_yaw * error_yaw / self.max_angular_velocity_limit)
                            cmd_vel.angular.z = max(min(cmd_vel.angular.z, self.max_angular_velocity_limit), -self.max_angular_velocity_limit)
                        elif self.use_tanh_law:
                            cmd_vel.angular.z = self.max_angular_velocity_limit * math.tanh(self.kp_yaw * error_yaw / self.max_angular_velocity_limit)
                            cmd_vel.angular.z = max(min(cmd_vel.angular.z, self.max_angular_velocity_limit), -self.max_angular_velocity_limit)
                        else:
                            cmd_vel.angular.z = max(min(self.kp_yaw * error_yaw, self.max_angular_velocity_limit), -self.max_angular_velocity_limit)

                        if cmd_vel.angular.z < 0.0:
                            cmd_vel.angular.z = min(cmd_vel.angular.z, -self.min_angular_velocity_limit)
                        elif cmd_vel.angular.z > 0.0:
                            cmd_vel.angular.z = max(cmd_vel.angular.z, self.min_angular_velocity_limit)
                        else:
                            cmd_vel.angular.z = 0.0                       
                        rospy.logwarn("docking_stage == 4 cmd_vel.angular.z: %f", cmd_vel.angular.z)
                    else:
                        rospy.loginfo("yaw error reduced to 0.03")
                        cmd_vel.linear.x = 0
                        cmd_vel.linear.y = 0
                        cmd_vel.linear.z = -1
                        cmd_vel.angular.z = 0
                        # self.docking_state = False
                        self.docking_stage = 1
                        rospy.loginfo("error_x: %f error_y: %f error_yaw: %f", error_x, error_y, error_yaw)

                if self.docking_stage == 1:
                    if abs(error_x) >= self.error_threshold or abs(error_y) >= self.error_threshold or abs(error_yaw) >= self.error_threshold_yaw:
                        if self.use_sinh_law:
                            # Use hyperbolic sine-based control law V = Vmax*Sinh(K * Error /Vmax)
                            cmd_vel.linear.x = self.max_linear_velocity_limit * math.sinh(self.kp_x * error_x / self.max_linear_velocity_limit) * backwards_docking_factor
                            cmd_vel.linear.y = self.max_linear_velocity_limit * math.sinh(self.kp_y * error_y / self.max_linear_velocity_limit)
                            cmd_vel.angular.z = self.max_angular_velocity_limit * math.sinh(self.kp_yaw * error_yaw / self.max_angular_velocity_limit)

                            # Handle negative and positive velocities
                            cmd_vel.linear.x = max(min(cmd_vel.linear.x, self.max_linear_velocity_limit), -self.max_linear_velocity_limit)
                            cmd_vel.linear.y = max(min(cmd_vel.linear.y, self.max_linear_velocity_limit), -self.max_linear_velocity_limit)
                            cmd_vel.angular.z = max(min(cmd_vel.angular.z, self.max_angular_velocity_limit), -self.max_angular_velocity_limit)
                        elif self.use_tanh_law:
                            # Use hyperbolic tangent-based control law
                            cmd_vel.linear.x = self.max_linear_velocity_limit * math.tanh(self.kp_x * error_x / self.max_linear_velocity_limit) * backwards_docking_factor
                            cmd_vel.linear.y = self.max_linear_velocity_limit * math.tanh(self.kp_y * error_y / self.max_linear_velocity_limit)
                            cmd_vel.angular.z = self.max_angular_velocity_limit * math.tanh(self.kp_yaw * error_yaw / self.max_angular_velocity_limit)         
                            # Handle negative and positive velocities
                            cmd_vel.linear.x = max(min(cmd_vel.linear.x, self.max_linear_velocity_limit), -self.max_linear_velocity_limit) 
                            cmd_vel.linear.y = max(min(cmd_vel.linear.y, self.max_linear_velocity_limit), -self.max_linear_velocity_limit)
                            cmd_vel.angular.z = max(min(cmd_vel.angular.z, self.max_angular_velocity_limit), -self.max_angular_velocity_limit)
                        elif self.use_diff_law:
                            cmd_vel.linear.x = self.max_linear_velocity_limit * math.sinh(self.kp_x * error_x / self.max_linear_velocity_limit) * backwards_docking_factor - self.kp_y * error_y
                            cmd_vel.angular.z = self.max_angular_velocity_limit * math.sinh(self.kp_yaw * error_yaw / self.max_angular_velocity_limit) - self.kp_y * error_y
                            # Handle negative and positive velocities
                            cmd_vel.linear.x = max(min(cmd_vel.linear.x, self.max_linear_velocity_limit), -self.max_linear_velocity_limit) 
                            cmd_vel.angular.z = max(min(cmd_vel.angular.z, self.max_angular_velocity_limit), -self.max_angular_velocity_limit)             
                        else:
                            # Use proportional (Kp-based) control law
                            cmd_vel.linear.x = max(min(self.kp_x * error_x, self.max_linear_velocity_limit), -self.max_linear_velocity_limit) * backwards_docking_factor
                            cmd_vel.linear.y = max(min(self.kp_y * error_y, self.max_linear_velocity_limit), -self.max_linear_velocity_limit)
                            cmd_vel.angular.z = max(min(self.kp_yaw * error_yaw, self.max_angular_velocity_limit), -self.max_angular_velocity_limit)
                        
                        # rospy.logwarn("docking_stage == 1 cmd_vel.linear.x: %f", cmd_vel.linear.x)
                        # rospy.logwarn("docking_stage == 1 cmd_vel.linear.y: %f", cmd_vel.linear.y)
                        # rospy.logwarn("docking_stage == 1 cmd_vel.angular.z: %f", cmd_vel.angular.z)
                        current_velocities = [cmd_vel.linear.x, cmd_vel.linear.y, cmd_vel.angular.z]
                        if not self.calc_valid_velocities(*current_velocities):
                            cmd_vel.linear.y = cmd_vel.linear.y / 2
                            rospy.logerr("Invalid Swereve cmd reducing Y to: %f", cmd_vel.linear.y)
                            cmd_vel.angular.z = 0.0

                        # Ensure the linear velocity is within the specified limits
                        if cmd_vel.linear.x < 0.0:
                            cmd_vel.linear.x = min(cmd_vel.linear.x, -self.min_linear_velocity_limit_x)
                        elif cmd_vel.linear.x > 0.0:
                            cmd_vel.linear.x = max(cmd_vel.linear.x, self.min_linear_velocity_limit_x)
                        else:
                            cmd_vel.linear.x = 0.0

                        if cmd_vel.linear.y < 0.0:
                            cmd_vel.linear.y = min(cmd_vel.linear.y, -self.min_linear_velocity_limit_y)
                        elif cmd_vel.linear.y > 0.0:
                            cmd_vel.linear.y = max(cmd_vel.linear.y, self.min_linear_velocity_limit_y)
                        else:
                            cmd_vel.linear.y = 0.0

                        if cmd_vel.angular.z < 0.0:
                            cmd_vel.angular.z = min(cmd_vel.angular.z, -self.min_angular_velocity_limit)
                        elif cmd_vel.angular.z > 0.0:
                            cmd_vel.angular.z = max(cmd_vel.angular.z, self.min_angular_velocity_limit)
                        else:
                            cmd_vel.angular.z = 0.0
                        rospy.logwarn("docking_stage == 1 cmd_vel.linear.x: %f", cmd_vel.linear.x)
                        rospy.logwarn("docking_stage == 1 cmd_vel.linear.y: %f", cmd_vel.linear.y)
                        rospy.logwarn("docking_stage == 1 cmd_vel.angular.z: %f", cmd_vel.angular.z)
                    else:
                        rospy.loginfo("Goal reached. Stopping the robot.")
                        cmd_vel.linear.x = 0
                        cmd_vel.linear.y = 0
                        cmd_vel.linear.z = -1
                        cmd_vel.angular.z = 0
                        # self.docking_state = False
                        self.docking_stage = 2
                        rospy.loginfo("error_x: %f error_y: %f error_yaw: %f", error_x, error_y, error_yaw)
                        sleep = True

                elif self.docking_stage == 2:
                    if abs(error_x) >= self.error_threshold:
                        if self.use_sinh_law:
                            # Use hyperbolic sine-based control law V = Vmax*Sinh(K * Error /Vmax)
                            cmd_vel.linear.x = self.max_linear_velocity_limit * math.sinh(self.kp_x * error_x / self.max_linear_velocity_limit) * backwards_docking_factor
                            cmd_vel.linear.x = max(min(cmd_vel.linear.x, self.max_linear_velocity_limit), -self.max_linear_velocity_limit)
                        elif self.use_tanh_law:
                            # Use hyperbolic tangent-based control law
                            cmd_vel.linear.x = self.max_linear_velocity_limit * math.tanh(self.kp_x * error_x / self.max_linear_velocity_limit) * backwards_docking_factor
                            cmd_vel.linear.x = max(min(cmd_vel.linear.x, self.max_linear_velocity_limit), -self.max_linear_velocity_limit)
                        else:
                            cmd_vel.linear.x = max(min(self.kp_x * error_x, self.max_linear_velocity_limit), -self.max_linear_velocity_limit) * backwards_docking_factor
                        # cmd_vel.linear.y = max(min(self.kp_y * error_y, self.max_linear_velocity_limit), -self.max_linear_velocity_limit)

                        # Ensure the linear velocity is within the specified limits
                        if cmd_vel.linear.x < 0.0:
                            cmd_vel.linear.x = min(cmd_vel.linear.x, -self.min_linear_velocity_limit_x)
                        elif cmd_vel.linear.x > 0.0:
                            cmd_vel.linear.x = max(cmd_vel.linear.x, self.min_linear_velocity_limit_x)
                        else:
                            cmd_vel.linear.x = 0.0

                        rospy.logwarn_throttle_identical(1,"docking_stage == 2 cmd_vel.linear.x: %f", cmd_vel.linear.x)
                        rospy.logwarn_throttle_identical(1,"docking_stage == 2 cmd_vel.linear.y: %f", cmd_vel.linear.y)
                        rospy.logwarn_throttle_identical(1,"docking_stage == 2 cmd_vel.angular.z: %f", cmd_vel.angular.z)
                        
                    else:
                        if abs(error_y) >= self.error_threshold or abs(error_yaw) >= self.error_threshold_yaw:
                            self.docking_stage = 1
                            cmd_vel.linear.x = 0
                            cmd_vel.linear.y = 0
                            cmd_vel.linear.z = -1
                            cmd_vel.angular.z = 0
                            rospy.loginfo("Recovery initiated as error_x: %f error_y: %f error_yaw: %f", error_x, error_y, error_yaw)

                        else:
                            rospy.loginfo("Goal reached. Stopping the robot.")
                            cmd_vel.linear.x = 0
                            cmd_vel.linear.y = 0
                            cmd_vel.linear.z = -1
                            cmd_vel.angular.z = 0
                            # self.docking_state = False
                            self.docking_stage = 3
                            rospy.loginfo("error_x: %f error_y: %f error_yaw: %f", error_x, error_y, error_yaw)
                elif self.docking_stage == 3:
                    if abs(error_x) >= self.error_threshold:
                        if self.use_sinh_law:
                            # Use hyperbolic sine-based control law V = Vmax*Sinh(K * Error /Vmax)
                            cmd_vel.linear.x = self.max_linear_velocity_limit * math.sinh(self.kp_x * error_x / self.max_linear_velocity_limit) * backwards_docking_factor
                            cmd_vel.linear.x = max(min(cmd_vel.linear.x, self.max_linear_velocity_limit), -self.max_linear_velocity_limit)
                        elif self.use_tanh_law:
                            # Use hyperbolic tangent-based control law
                            cmd_vel.linear.x = self.max_linear_velocity_limit * math.tanh(self.kp_x * error_x / self.max_linear_velocity_limit) * backwards_docking_factor
                            cmd_vel.linear.x = max(min(cmd_vel.linear.x, self.max_linear_velocity_limit), -self.max_linear_velocity_limit)
                        else:
                            cmd_vel.linear.x = max(min(self.kp_x * error_x, self.max_linear_velocity_limit), -self.max_linear_velocity_limit) * backwards_docking_factor
                        # cmd_vel.linear.y = max(min(self.kp_y * error_y, self.max_linear_velocity_limit), -self.max_linear_velocity_limit)

                        # Ensure the linear velocity is within the specified limits
                        if cmd_vel.linear.x < 0.0:
                            cmd_vel.linear.x = min(cmd_vel.linear.x, -self.min_linear_velocity_limit_x)
                        elif cmd_vel.linear.x > 0.0:
                            cmd_vel.linear.x = max(cmd_vel.linear.x, self.min_linear_velocity_limit_x)
                        else:
                            cmd_vel.linear.x = 0.0

                        rospy.logwarn_throttle_identical(1,"docking_stage == 3 cmd_vel.linear.x: %f", cmd_vel.linear.x)
                        rospy.logwarn_throttle_identical(1,"docking_stage == 3 cmd_vel.linear.y: %f", cmd_vel.linear.y)
                        rospy.logwarn_throttle_identical(1,"docking_stage == 3 cmd_vel.angular.z: %f", cmd_vel.angular.z)
                        
                    else:
                        rospy.loginfo("Goal reached. Stopping the robot.")
                        cmd_vel.linear.x = 0
                        cmd_vel.linear.y = 0
                        cmd_vel.linear.z = -1
                        cmd_vel.angular.z = 0
                        self.docking_state = False
                        self.docking_stage = 5
                        rospy.loginfo("error_x: %f error_y: %f error_yaw: %f", error_x, error_y, error_yaw)
                        # Write the values to a CSV file
                        csv_data = "{},{},{}\n".format(error_x, error_y, error_yaw)
                        csv_file_path = "/home/<USER>/rover_stack/rover_ws/src/navigation/otto_docking/scripts/accuray_file.csv"

                        try:
                            with open(csv_file_path, 'a') as csv_file:
                                csv_file.write(csv_data)
                            rospy.loginfo("Values written to {}".format(csv_file_path))
                        except Exception as e:
                            rospy.logerr("Error writing to CSV file: {}".format(e))

                if sleep:
                    rospy.logerr("SLEEP FOR SOME TIME")
                    self.cmd_vel_pub.publish(cmd_vel)
                    sleep = False
                    rospy.sleep(2.0)
                else: 
                    self.cmd_vel_pub.publish(cmd_vel)

            self.rate.sleep()

if __name__ == '__main__':
    try:
        auto_docking = OttoDocking()
        auto_docking.dock()
    except rospy.ROSInterruptException:
        pass
