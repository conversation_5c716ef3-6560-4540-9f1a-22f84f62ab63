#ifndef DOCKING_H_
#define DOCKING_H_
#include <ros/ros.h>
#include <tf2_ros/transform_listener.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.h>
#include <geometry_msgs/PoseStamped.h>
#include <geometry_msgs/Pose.h>
#include <std_srvs/SetBool.h>
#include <geometry_msgs/Twist.h>
#include <visualization_msgs/Marker.h>
#include <apriltag_ros/AprilTagDetectionArray.h>
#include <Eigen/Dense>
#include <cmath>
#include <vector>
#include <dynamic_reconfigure/server.h>
#include <otto_docking/OttoDockingConfig.h>
#include <boost/thread.hpp>
#include <actionlib_msgs/GoalStatusArray.h>
#include <ottonomy_msgs/OttoDockAction.h>
#include <ottonomy_msgs/DockingState.h>
#include <ottonomy_msgs/DockingFeedback.h>
#include <ottonomy_msgs/RobotIOStates.h>
#include <ottonomy_msgs/RobotInfo.h>
#include <ottonomy_msgs/SetDockingFlags.h>
#include <ottonomy_msgs/MotorDriverFeedback.h>
#include <actionlib/server/simple_action_server.h>
#include <boost/circular_buffer.hpp>
#include <numeric>
#include <angles/angles.h>
#include <std_msgs/String.h>

class OttoDocking
{
public:
    OttoDocking(ros::NodeHandle);

    ~OttoDocking();

    bool load_params();

    bool start_apriltag_detection(bool status);

    /**
     * @brief call service to start/establish the communication between robot <> docking station
     *
     * @param status
     * @return true
     * @return false
     */
    bool hardware_start_communication(int status);

    /**
     * @brief timer callback function to publish the status of docking state
     *
     */
    void publish_docking_status(const ros::TimerEvent&);

    /**
     * @brief call service to start/stop the charging
     *
     * @param status
     * @return true
     * @return false
     */
    bool hardware_start_charging(int status);

    void robot_io_subscriber_callback(const ottonomy_msgs::RobotIOStates &robot_io_states);

    void robot_info_subscriber_callback(const ottonomy_msgs::RobotInfo &robot_info);

    void motor_driver_1_feedback_callback(const ottonomy_msgs::MotorDriverFeedback &feedback);

    void motor_driver_2_feedback_callback(const ottonomy_msgs::MotorDriverFeedback &feedback);

    bool force_apply_brakes(bool status);

    bool docking_drive_mode(bool status);

    bool reduce_back_safety(bool status);

    bool disengage_motors(bool status, const int max_retries);

    bool service_handler(bool status, std::string service_name, const int max_retries = 20);

    /**
     * @brief callback function for detected april tags.
     *
     * @param marker Detected Markers
     */
    void detection_callback(const apriltag_ros::AprilTagDetectionArray &marker);

    /**
     * @brief function to calculate yaw error
     *
     * @param pose1 pose from which yaw error is calculated
     * @return double error value
     */
    double calculate_yaw_error(const geometry_msgs::Pose &pose1);

    // Given 2 marker poses in camera frame, get dock_pose_contact, dock_pose_1, dock_pose_2 and undock_pose
    void create_line_pose(const geometry_msgs::Pose &pose1, const geometry_msgs::Pose &pose2, const std_msgs::Header &header);

    void get_virtual_docking_pose(const geometry_msgs::PoseStamped &pose_in_map_frame,
                                  geometry_msgs::PoseStamped &virtual_docking_pose);

    /**
     * @brief function to publish visualization maker
     *
     * @param poseStamped pose on which the marker needs to be placed
     * @param ns namespace
     * @param color color of the maker
     * @param size size of the marker
     */
    void publish_marker_point(const geometry_msgs::PoseStamped &poseStamped, const std::string &ns, const std::vector<double> &color, double size);

    void calc_error_given_target_pose(const geometry_msgs::PoseStamped target_pose, float &error_x, float &error_y, float &error_yaw);

    void calc_valid_vel_given_error(float error_x, float error_y, float error_yaw,
                                    float &Vx, float &Vy, float &W);

    // Function to identify if the swerve command is possible or not
    bool check_if_velocities_are_valid(const double V, const double theta, double W_in);

    // /**
    //  * @brief dynamicly reconfiguerable parameters
    //  *
    //  * @param config
    //  * @param level
    //  */
    // void reconfigureCB(otto_docking::OttoDockingConfig &config, uint32_t level);
    // dynamic_reconfigure::Server<otto_docking::OttoDockingConfig> *dynamicSrv;
    // boost::recursive_mutex lock_;
    inline void print_and_publish_log(const std::string &msg);

    /**
     * @brief Ros Service callback function for locker_door_is opened.
     *
     * @param req
     * @param res
     * @return true
     * @return false
     */
    bool locker_door_opened_srv_cb(std_srvs::SetBool::Request &req, std_srvs::SetBool::Response &res);

    inline void publish_velocity(const float Vx, const float Vy, const float W);

    bool move_to_location(const geometry_msgs::PoseStamped &target_pose, uint16_t time_limit_sec,
                          const float error_threshold_xy, const float error_threshold_yaw,
                          bool move_only_in_x, bool &target_lost, bool &prempt_requested);

    void execute_cb(const ottonomy_msgs::OttoDockGoalConstPtr &otto_docking);

    ros::NodeHandle nh;
    tf2_ros::Buffer tf_buffer;
    tf2_ros::TransformListener tf_listener;
    ros::Subscriber target_pose_subscriber;
    ros::Publisher cmd_vel_pub;
    ros::Publisher marker_pub;
    ros::Publisher docking_state_pub;
    ros::Publisher current_docking_state_pub_;
    ros::ServiceServer locker_door_opened_srv_;

    // std_msgs::String curent_docking_state_msg; // for publishing current docking state of the robot
    ros::Timer timerCallback_;

    typedef actionlib::SimpleActionServer<ottonomy_msgs::OttoDockAction> OttoDockActionServer;
    OttoDockActionServer *dockAS;
    std::string goal_abort_reason_;

    // Global variables
    boost::mutex docking_target_pose_mutex;
    ros::Time target_detection_time;
    std::string docking_id;
    int station_id;
    int docking_type;
    int docking_state;
    int previous_docking_state;

    geometry_msgs::TransformStamped baseLinkNav_camera_tf;
    geometry_msgs::PoseStamped dock_pose_contact; // docking_distance from marker_mid_pose
    geometry_msgs::PoseStamped dock_pose_1;       // 0.1m from dock_pose
    geometry_msgs::PoseStamped dock_pose_2;       // 0.3m from dock_pose
    geometry_msgs::PoseStamped undock_pose;       // undocking_distance from marker_mid_pose

    // Hardware feedback
    ros::Subscriber robot_io_subscriber_;
    boost::mutex hardware_feedback_mutex_;
    int contact_force_buffer_size_, cliff_sensor_buffer_size_;
    boost::circular_buffer<int> contact_force_1_buffer_, contact_force_2_buffer_, cliff_sensor_1_buffer_, cliff_sensor_2_buffer_; /////
    double contact_force_1_average_, contact_force_2_average_, cliff_sensor_1_average_, cliff_sensor_2_average_;                  /////
    int communication_flag_status_, charging_flag_status_;
    ros::Time hardware_feedback_time_;

    ros::Subscriber motor_driver_1_feedback_subscriber_, motor_driver_2_feedback_subscriber_;
    boost::mutex motor_driver_feedback_mutex_;
    ottonomy_msgs::MotorDriverFeedback motor_driver_feedback_1_, motor_driver_feedback_2_;

    ros::Subscriber robot_info_subscriber_;
    boost::mutex robot_info_mutex_;
    ottonomy_msgs::RobotInfo robot_info_;

    // Enum
    enum DOCKING_TYPE
    {
        DOCKING,
        VIRTUALDOCKING_TYPE,
        UNDOCKING_TYPE,
        MANUALDOCKING,
        LOCKERDOCKING,
        LOCKERUNDOCKING 
    };

    enum DOCKING_STATE
    {
        VIRTUALDOCKING,
        START_APRILTAG,
        STOP_APRILTAG,
        MOVETO_POSE_2,
        HARDWARE_START_COMM,
        MOVETO_POSE_1,
        MOVETO_CONTACT,
        HARDWARE_START_CHARGING,
        HARDWARE_CHECK_CHARGING,
        HARDWARE_STOP_COMM_CHARGING,
        UNDOCKING,
        DOCKING_COMPLETED,
        ROTATE_RECOVERY,
        DOCKING_FAILED,
        DOCKING_PREMPTED,
        INIT,             //
        WAITING_FOR_LOCKER_DOOR_OPEN,
        DOCKING_WITH_CLIFF,
        UNDOCKING_WITH_CLIFF
    };

    std::map<int, std::string>docking_state_to_string;
    std::map<int, std::string>docking_type_to_string;

    // Parameters
    bool backwards_docking;
    double marker_y_offset;
    double marker_yaw_offset;
    float error_threshold_xy, error_threshold_yaw;
    float error_threshold_virtualdocking_xy, error_threshold_virtualdocking_yaw;
    float max_linear_velocity_limit, min_linear_velocity_limit, max_angular_velocity_limit, min_angular_velocity_limit;
    float kp_x, kp_y, kp_yaw;
    bool use_sinh_law, use_tanh_law;
    float docking_distance, undocking_distance;
    std::string camera_frame;
    float max_action_time_sec;
    float max_target_lost_time_for_moving_robot;
    float target_lost_time_for_recovery;
    int num_retries, max_docking_retries;
    bool communicate_with_dock_;
    int contact_force_threshold_;
    int min_contact_force_threshold_;
    bool use_motor_current_instead_of_contact_force_;
    float motor_amps_threshold_;
    bool simulate_;
    bool is_lockerdocking_; /// added
    float pose_x_offset_;
    int cliff_sensor_threshold_;
    int min_cliff_sensor_docking_threshold_;
    int min_cliff_sensor_undocking_threshold_;
    bool locker_door_opened_flag_;
    bool locker_door_always_opened_flag_;
    int cliff_buffer_size_;
    std::string robot_type_;
    std::string current_message_;
};

#endif // DOCKING_H_
