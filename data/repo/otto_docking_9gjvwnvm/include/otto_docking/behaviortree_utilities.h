#pragma once

#include "behaviortree_cpp/bt_factory.h"

#include "behaviortree_cpp/loggers/bt_cout_logger.h"
#include "behaviortree_cpp/loggers/bt_observer.h"

#include "ros/ros.h"
#include "geometry_msgs/PoseStamped.h"
#include "std_msgs/String.h"
#include <string>
#include <sstream>
#include <iomanip>
#include <fstream>

// Template specialization to converts a string to geometry_msgs::PoseStamped
namespace BT
{
    template <>
    inline geometry_msgs::PoseStamped convertFromString(StringView str)
    {
        auto parts = splitString(str, ';');
        if (parts.size() != 7)
        {
            ROS_ERROR_STREAM("invalid input, num parts != 7 for geometry_msgs::PoseStamped");
            throw RuntimeError("invalid input, num parts != 7 for geometry_msgs::PoseStamped");
        }
        else
        {
            geometry_msgs::PoseStamped output;
            output.header.frame_id = convertFromString<std::string>(parts[0]);
            output.pose.position.x = convertFromString<double>(parts[1]);
            output.pose.position.y = convertFromString<double>(parts[2]);
            output.pose.orientation.x = convertFromString<double>(parts[3]);
            output.pose.orientation.y = convertFromString<double>(parts[4]);
            output.pose.orientation.z = convertFromString<double>(parts[5]);
            output.pose.orientation.w = convertFromString<double>(parts[6]);
            return output;
        }
    }
} // end namespace BT

// Function to convert geometry_msgs::PoseStamped to a string
std::string poseStampedToString(const geometry_msgs::PoseStamped &pose_stamped)
{
    std::stringstream ss;

    // Extract and format the data
    ss << pose_stamped.header.frame_id << "; "
       << std::fixed << std::setprecision(3) << pose_stamped.pose.position.x << "; "
       << std::fixed << std::setprecision(3) << pose_stamped.pose.position.y << "; "
       << std::fixed << std::setprecision(3) << pose_stamped.pose.orientation.x << "; "
       << std::fixed << std::setprecision(3) << pose_stamped.pose.orientation.y << "; "
       << std::fixed << std::setprecision(3) << pose_stamped.pose.orientation.z << "; "
       << std::fixed << std::setprecision(3) << pose_stamped.pose.orientation.w;

    return ss.str();
}

class TransitionLogger : public BT::TreeObserver
{
public:
    TransitionLogger(const BT::Tree &tree, ros::Publisher &publisher)
        : BT::TreeObserver(tree), publisher_(publisher) {}

    void callback(BT::Duration timestamp, const BT::TreeNode &node, BT::NodeStatus prev_status,
                  BT::NodeStatus status)
    {
        std_msgs::String msg;
        std::stringstream ss;
        ss << ">> Node [" << node.name() << "] transitioned from ["
           << BT::toStr(prev_status) << "] to [" << BT::toStr(status) << "]";
        msg.data = ss.str();
        publisher_.publish(msg);

        ROS_INFO_STREAM(ss.str());
    }

private:
    ros::Publisher &publisher_;
};

void writeStringToFile(const std::string &filename, const std::string &content)
{
    // Open the file in write mode
    std::ofstream outFile(filename);

    // Check if the file was opened successfully
    if (!outFile.is_open())
    {
        std::cerr << "Error: Could not open file " << filename << std::endl;
        return;
    }

    // Write the content to the file
    outFile << content;

    // Close the file
    outFile.close();
}
