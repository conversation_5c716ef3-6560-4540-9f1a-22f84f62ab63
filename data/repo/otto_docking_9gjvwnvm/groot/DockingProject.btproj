<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4" project_name="DockingProject">
    <include path="docking.xml"/>
    <include path="undocking.xml"/>
    <include path="virtualdocking.xml"/>
    <!-- Description of Node Models (used by Groot) -->
    <TreeNodesModel>
        <Condition ID="AbortActionGoal"/>
        <Action ID="ApriltagDetectionStart"/>
        <Action ID="ApriltagDetectionStop"/>
        <Action ID="CheckIfMapPoseIsTooFar">
            <input_port name="target_pose" type="geometry_msgs::PoseStamped_&lt;std::allocator&lt;void&gt; &gt;"/>
        </Action>
        <Action ID="DisengageMotors"/>
        <Action ID="DockingDriveModeStart"/>
        <Action ID="DockingDriveModeStop"/>
        <Action ID="EngageMotors"/>
        <Condition ID="IsPreemptRequested"/>
        <Action ID="IsRobotCharging"/>
        <Action ID="MoveToApriltagPose">
            <input_port name="y_offset" type="float"/>
            <input_port name="x_offset" type="float"/>
            <input_port name="move_only_in_x" type="bool"/>
        </Action>
        <Action ID="MoveToMapPose">
            <input_port name="target_pose" type="geometry_msgs::PoseStamped_&lt;std::allocator&lt;void&gt; &gt;"/>
        </Action>
        <Condition ID="PremptActionGoal"/>
        <Action ID="ReduceBackSafetyStart"/>
        <Action ID="ReduceBackSafetyStop"/>
        <Action ID="StartDockCharging"/>
        <Action ID="StartDockCommunication"/>
        <Action ID="StopDockCharging"/>
        <Action ID="StopDockCommunication"/>
        <Condition ID="SucceededActionGoal"/>
    </TreeNodesModel>
</root>
