#include <ros/ros.h>
#include <actionlib/client/simple_action_client.h>
#include <actionlib/client/terminal_state.h>
#include <ottonomy_msgs/OttoDockAction.h>
#include <ottonomy_msgs/OttoDockActionGoal.h>

typedef actionlib::SimpleActionClient<ottonomy_msgs::OttoDockAction> DockingActionClient;

int main(int argc, char **argv)
{
    ros::init(argc, argv, "otto_dock_loop_test_node");
    ros::NodeHandle nh;

    DockingActionClient docking_client("/otto_docking", true);

    ROS_INFO("Waiting for action servers to start.");
    docking_client.waitForServer();
    ROS_INFO("Action servers started.");

    for (int i = 0; i < 50; ++i)
    {
        ottonomy_msgs::OttoDockGoal docking_goal;
        docking_goal.header.frame_id = "map";
        docking_goal.id = std::to_string(ros::Time::now().toSec());
        docking_goal.station_id = "stn1";
        docking_goal.target_pose.header.frame_id = "map";
        docking_goal.type = "docking";
        docking_goal.target_pose.pose.position.x = 2.820;
        docking_goal.target_pose.pose.position.y = 11.393;
        docking_goal.target_pose.pose.position.z = 0.0;
        docking_goal.target_pose.pose.orientation.x = -0.001;
        docking_goal.target_pose.pose.orientation.y = -0.001;
        docking_goal.target_pose.pose.orientation.z = 0.588;
        docking_goal.target_pose.pose.orientation.w = 0.809;

        ROS_INFO("Sending docking goal %d", i);
        docking_client.sendGoal(docking_goal);

        // Wait for the action to return
        bool finished_before_timeout = docking_client.waitForResult(ros::Duration(5*60.0));
        if (finished_before_timeout)
        {
            actionlib::SimpleClientGoalState state = docking_client.getState();
            ROS_INFO("Docking Action finished: %s", state.toString().c_str());

            if (state != state.SUCCEEDED)
            {
                exit(0);
            }
        }
        else
        {
            ROS_INFO("Docking Action did not finish before the timeout.");
            exit(0);
        }

        ROS_INFO("Waiting for 30 sec");
        ros::Duration(30.0).sleep();

        // start undocking process
        ottonomy_msgs::OttoDockGoal undocking_goal;
        undocking_goal.header.frame_id = "map";
        undocking_goal.id = std::to_string(ros::Time::now().toSec());
        undocking_goal.station_id = "stn1";
        undocking_goal.target_pose.header.frame_id = "map";
        undocking_goal.type = "undocking";

        ROS_INFO("Sending undocking goal %d", i);
        docking_client.sendGoal(undocking_goal);

        // Wait for the action to return
        finished_before_timeout = docking_client.waitForResult(ros::Duration(5*60.0));
        if (finished_before_timeout)
        {
            actionlib::SimpleClientGoalState state = docking_client.getState();
            ROS_INFO("Undocking Action finished: %s", state.toString().c_str());

            if (state != state.SUCCEEDED)
            {
                exit(0);
            }
        }
        else
        {
            ROS_INFO("Undocking Action did not finish before the timeout.");
            exit(0);
        }

        ROS_INFO("Waiting for 15 sec");
        ros::Duration(15.0).sleep();
    }

    return 0;
}
