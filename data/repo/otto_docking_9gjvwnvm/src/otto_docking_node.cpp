#include <otto_docking/otto_docking_node.h>

OttoDocking::OttoDocking(ros::NodeHandle n) : nh(n), tf_listener(tf_buffer)
{
    target_detection_time = ros::Time(0);
    target_pose_subscriber = nh.subscribe("/tag_detections", 1, &OttoDocking::detection_callback, this);
    marker_pub = nh.advertise<visualization_msgs::Marker>("docking_markers", 1);
    cmd_vel_pub = nh.advertise<geometry_msgs::Twist>("/cmd_vel", 1);
    docking_state_pub = nh.advertise<ottonomy_msgs::DockingState>("docking_state", 1);
    current_docking_state_pub_ = nh.advertise<ottonomy_msgs::DockingState>("/current_docking_status",1);
    locker_door_opened_srv_ = nh.advertiseService("/locker_door_opened", &OttoDocking::locker_door_opened_srv_cb, this);
    timerCallback_ = nh.createTimer(ros::Duration(1.0), &OttoDocking::publish_docking_status, this);

    docking_state_to_string[VIRTUALDOCKING] = "VIRTUALDOCKING";
    docking_state_to_string[START_APRILTAG] = "START_APRILTAG";
    docking_state_to_string[STOP_APRILTAG] = "STOP_APRILTAG";
    docking_state_to_string[MOVETO_POSE_2] = "MOVETO_POSE_2";
    docking_state_to_string[HARDWARE_START_COMM] = "HARDWARE_START_COMM";
    docking_state_to_string[MOVETO_POSE_1] = "MOVETO_POSE_1";
    docking_state_to_string[MOVETO_CONTACT] = "MOVETO_CONTACT";
    docking_state_to_string[HARDWARE_START_CHARGING] = "HARDWARE_START_CHARGING";
    docking_state_to_string[HARDWARE_CHECK_CHARGING] = "HARDWARE_CHECK_CHARGING";
    docking_state_to_string[HARDWARE_STOP_COMM_CHARGING] = "HARDWARE_STOP_COMM_CHARGING";
    docking_state_to_string[UNDOCKING] = "UNDOCKING";
    docking_state_to_string[DOCKING_COMPLETED] = "DOCKING_COMPLETED";
    docking_state_to_string[ROTATE_RECOVERY] = "ROTATE_RECOVERY";
    docking_state_to_string[DOCKING_FAILED] = "DOCKING_FAILED";
    docking_state_to_string[DOCKING_PREMPTED] = "DOCKING_PREMPTED";
    docking_state_to_string[INIT] = "INIT";
    docking_state_to_string[WAITING_FOR_LOCKER_DOOR_OPEN] = "WAITING_FOR_LOCKER_DOOR_OPEN";
    docking_state_to_string[DOCKING_WITH_CLIFF] = "DOCKING_WITH_CLIFF";
    docking_state_to_string[UNDOCKING_WITH_CLIFF] = "UNDOCKING_WITH_CLIFF";

    docking_type_to_string[DOCKING] = "DOCKING";
    docking_type_to_string[VIRTUALDOCKING_TYPE] = "VIRTUALDOCKING_TYPE";
    docking_type_to_string[UNDOCKING_TYPE] = "UNDOCKING_TYPE";
    docking_type_to_string[MANUALDOCKING] = "MANUALDOCKING";
    docking_type_to_string[LOCKERDOCKING] = "LOCKERDOCKING";
    docking_type_to_string[LOCKERUNDOCKING] = "LOCKERUNDOCKING";
    

    if (!load_params())
    {
        exit(0);
    }

    cliff_sensor_1_buffer_.resize(cliff_buffer_size_, 0.0);
    cliff_sensor_2_buffer_.resize(cliff_buffer_size_, 0.0);

    if (communicate_with_dock_)
    {
        contact_force_1_buffer_.resize(contact_force_buffer_size_, 0.0);
        contact_force_2_buffer_.resize(contact_force_buffer_size_, 0.0);

        hardware_feedback_time_ = ros::Time(0);
        robot_io_subscriber_ = nh.subscribe("/robot_io_states", 1, &OttoDocking::robot_io_subscriber_callback, this);
        robot_info_subscriber_ = nh.subscribe("/ottonomy_robot_node/robot_info", 1, &OttoDocking::robot_info_subscriber_callback, this);

        if (use_motor_current_instead_of_contact_force_)
        {
            motor_driver_1_feedback_subscriber_ = nh.subscribe("/ottonomy_robot_node/R1_motor_driver_feedback", 1, &OttoDocking::motor_driver_1_feedback_callback, this);
            motor_driver_2_feedback_subscriber_ = nh.subscribe("/ottonomy_robot_node/R2_motor_driver_feedback", 1, &OttoDocking::motor_driver_2_feedback_callback, this);
        }
    }

    // ros::NodeHandle private_nh("~");
    // dynamicSrv = new dynamic_reconfigure::Server<otto_docking::OttoDockingConfig>(private_nh);
    // dynamic_reconfigure::Server<otto_docking::OttoDockingConfig>::CallbackType cb = boost::bind(&OttoDocking::reconfigureCB, this, _1, _2);
    // dynamicSrv->setCallback(cb);

    bool got_camera_tf = false;
    while (!got_camera_tf)
    {
        try
        {
            baseLinkNav_camera_tf = tf_buffer.lookupTransform("base_link_nav", camera_frame, ros::Time(0));
            got_camera_tf = true;
        }
        catch (tf2::TransformException &ex)
        {
            ROS_WARN("Unable to get tf from base_link_nav to camera frame, error %s", ex.what());
            ros::Duration(0.5).sleep();
        }
    }
    ROS_INFO("Got tf from base_link_nav to camera frame");

    dockAS = new OttoDockActionServer(nh, "otto_docking", boost::bind(&OttoDocking::execute_cb, this, _1), false);
    dockAS->start();
}

OttoDocking::~OttoDocking()
{
    // delete dynamicSrv;

    if (dockAS != NULL)
        delete dockAS;
}

bool OttoDocking::load_params()
{
    ros::NodeHandle private_nh("~");

    if (!private_nh.getParam("camera_frame", camera_frame))
    {
        ROS_ERROR("Unable to load camera_frame param");
        return false;
    }

    if (!private_nh.getParam("docking_distance", docking_distance))
    {
        ROS_ERROR("Unable to load docking_distance param");
        return false;
    }

    if (!private_nh.getParam("undocking_distance", undocking_distance))
    {
        ROS_ERROR("Unable to load undocking_distance param");
        return false;
    }

    if (!private_nh.getParam("enable_backwards_docking", backwards_docking))
    {
        ROS_ERROR("Unable to load enable_backwards_docking param");
        return false;
    }

    if (!private_nh.getParam("marker_y_offset", marker_y_offset))
    {
        marker_y_offset = 0.0;
        ROS_WARN("Defaulting marker_y_offset param to %lf", marker_y_offset);
    }

    if (!private_nh.getParam("marker_yaw_offset", marker_yaw_offset))
    {
        marker_yaw_offset = 0.0;
        ROS_WARN("Defaulting marker_y_offset param to %lf", marker_yaw_offset);
    }

    if (!private_nh.getParam("error_threshold_xy", error_threshold_xy))
    {
        error_threshold_xy = 0.03;
        ROS_WARN("Defaulting error_threshold_xy param to %lf", error_threshold_xy);
    }

    if (!private_nh.getParam("error_threshold_yaw", error_threshold_yaw))
    {
        error_threshold_yaw = 0.04;
        ROS_WARN("Defaulting error_threshold_yaw param to %lf", error_threshold_yaw);
    }

    if (!private_nh.getParam("error_threshold_virtualdocking_xy", error_threshold_virtualdocking_xy))
    {
        error_threshold_virtualdocking_xy = 0.15;
        ROS_WARN("Defaulting error_threshold_virtualdocking_xy param to %lf", error_threshold_virtualdocking_xy);
    }

    if (!private_nh.getParam("error_threshold_virtualdocking_yaw", error_threshold_virtualdocking_yaw))
    {
        error_threshold_virtualdocking_yaw = 0.15;
        ROS_WARN("Defaulting error_threshold_virtualdocking_yaw param to %lf", error_threshold_virtualdocking_yaw);
    }

    if (!private_nh.getParam("max_linear_velocity_limit", max_linear_velocity_limit))
    {
        max_linear_velocity_limit = 0.2;
        ROS_WARN("Defaulting max_linear_velocity_limit param to %lf", max_linear_velocity_limit);
    }

    if (!private_nh.getParam("min_linear_velocity_limit", min_linear_velocity_limit))
    {
        min_linear_velocity_limit = 0.06;
        ROS_WARN("Defaulting min_linear_velocity_limit param to %lf", min_linear_velocity_limit);
    }

    if (!private_nh.getParam("max_angular_velocity_limit", max_angular_velocity_limit))
    {
        max_angular_velocity_limit = 0.2;
        ROS_WARN("Defaulting max_angular_velocity_limit param to %lf", max_angular_velocity_limit);
    }

    if (!private_nh.getParam("min_angular_velocity_limit", min_angular_velocity_limit))
    {
        min_angular_velocity_limit = 0.05;
        ROS_WARN("Defaulting min_angular_velocity_limit param to %lf", min_angular_velocity_limit);
    }

    if (!private_nh.getParam("max_action_time_sec", max_action_time_sec))
    {
        max_action_time_sec = 30.0;
        ROS_WARN("Defaulting min_angular_velocity_limit param to %lf", max_action_time_sec);
    }

    if (!private_nh.getParam("max_target_lost_time_for_moving_robot", max_target_lost_time_for_moving_robot))
    {
        max_target_lost_time_for_moving_robot = 0.6;
        ROS_WARN("Defaulting max_target_lost_time_for_moving_robot param to %lf", max_target_lost_time_for_moving_robot);
    }

    if (!private_nh.getParam("target_lost_time_for_recovery", target_lost_time_for_recovery))
    {
        target_lost_time_for_recovery = 5.0;
        ROS_WARN("Defaulting target_lost_time_for_recovery param to %lf", target_lost_time_for_recovery);
    }

    if (!private_nh.getParam("max_docking_retries", max_docking_retries))
    {
        max_docking_retries = 5;
        ROS_WARN("Defaulting max_docking_retries param to %d", max_docking_retries);
    }

    if (!private_nh.getParam("communicate_with_dock", communicate_with_dock_))
    {
        communicate_with_dock_ = false;
        ROS_WARN("Defaulting communicate_with_dock param to %d", communicate_with_dock_);
    }

    if (!private_nh.getParam("contact_force_threshold", contact_force_threshold_))
    {
        contact_force_threshold_ = 500;
        ROS_WARN("Defaulting contact_force_threshold param to %d", contact_force_threshold_);
    }

    if (!private_nh.getParam("min_contact_force_threshold", min_contact_force_threshold_))
    {
        min_contact_force_threshold_ = 50;
        ROS_WARN("Defaulting min_contact_force_threshold param to %d", min_contact_force_threshold_);
    }

    if (!private_nh.getParam("min_cliff_sensor_docking_threshold", min_cliff_sensor_docking_threshold_))
    {
        min_cliff_sensor_docking_threshold_ = 50;
        ROS_WARN("Defaulting min_cliff_sensor_docking_threshold param to %d", min_cliff_sensor_docking_threshold_);
    }
    ROS_WARN("Defaulting min_cliff_sensor_docking_threshold param to %d", min_cliff_sensor_docking_threshold_);

    if (!private_nh.getParam("min_cliff_sensor_undocking_threshold", min_cliff_sensor_undocking_threshold_))
    {
        min_cliff_sensor_undocking_threshold_ = 50;
        ROS_WARN("Defaulting min_cliff_sensor_undocking_threshold param to %d", min_cliff_sensor_undocking_threshold_);
    }
    ROS_WARN("Defaulting min_cliff_sensor_undocking_threshold param to %d", min_cliff_sensor_undocking_threshold_);

    if (!private_nh.getParam("contact_force_buffer_size", contact_force_buffer_size_))
    {
        contact_force_buffer_size_ = 5;
        ROS_WARN("Defaulting contact_force_buffer_size param to %d", contact_force_threshold_);
    }

    if (!private_nh.getParam("cliff_buffer_size", cliff_buffer_size_))
    {
        cliff_buffer_size_ = 1;
        ROS_WARN("Defaulting cliff_buffer_size_ param to %d", cliff_buffer_size_);
    }

    if (!private_nh.getParam("use_motor_current_instead_of_contact_force", use_motor_current_instead_of_contact_force_))
    {
        use_motor_current_instead_of_contact_force_ = false;
        ROS_WARN("Defaulting use_motor_current_instead_of_contact_force param to %d", use_motor_current_instead_of_contact_force_);
    }

    if (!private_nh.getParam("motor_amps_threshold", motor_amps_threshold_))
    {
        motor_amps_threshold_ = 30.0;
        ROS_WARN("Defaulting motor_amps_threshold param to %f", motor_amps_threshold_);
    }

    if (!private_nh.getParam("kp_x", kp_x))
    {
        kp_x = 0.8;
        ROS_WARN("Defaulting kp_x param to %lf", kp_x);
    }

    if (!private_nh.getParam("kp_y", kp_y))
    {
        kp_y = 0.05;
        ROS_WARN("Defaulting kp_y param to %lf", kp_y);
    }

    if (!private_nh.getParam("kp_yaw", kp_yaw))
    {
        kp_yaw = 0.2;
        ROS_WARN("Defaulting kp_yaw param to %lf", kp_yaw);
    }

    if (!private_nh.getParam("use_sinh_law", use_sinh_law))
    {
        use_sinh_law = true;
        ROS_WARN("Defaulting use_sinh_law param to %d", use_sinh_law);
    }

    if (!private_nh.getParam("use_tanh_law", use_tanh_law))
    {
        use_tanh_law = true;
        ROS_WARN("Defaulting use_tanh_law param to %d", use_tanh_law);
    }

    if (!private_nh.getParam("simulate", simulate_))
    {
        simulate_ = false;
        ROS_WARN("Defaulting simulate param to %d", simulate_);
    }
    if (!private_nh.getParam("pose_x_offset", pose_x_offset_))
    {
        pose_x_offset_ = 0.40;
        ROS_WARN("Defaulting pose_x_offset param to %lf", pose_x_offset_);
    }

    if (!private_nh.getParam("locker_door_always_opened", locker_door_always_opened_flag_))
    {
        locker_door_always_opened_flag_ = false;
        ROS_WARN("Defaulting locker_door_always_opened param to %d", locker_door_always_opened_flag_);
    }

    if (!private_nh.getParam("/ottonomy_robot/DRIVE_MODEL", robot_type_))
    {
        robot_type_ = "ackermann_4steer";
    }
    ROS_WARN("Using robot_type_ param to %d", robot_type_);
    return true;
}

/*void OttoDocking::reconfigureCB(otto_docking::OttoDockingConfig &config, uint32_t level)
{
    ROS_INFO("entered rq callback");

    boost::recursive_mutex::scoped_lock lock(lock_);
    ROS_INFO("passed mutex in rq callback");
    camera_frame = config.camera_frame;
    docking_distance = config.docking_distance;
    undocking_distance = config.undocking_distance;
    errorThreshold = config.error_threshold;
    errorThresholdYaw = config.error_threshold_yaw;
    max_docking_retries = config.docking_retries;
    kp_x = config.kp_x;
    kp_y = config.kp_y;
    kp_yaw = config.kp_yaw;
    max_linear_velocity_limit = config.max_linear_velocity_limit;
    min_linear_velocity_limit = config.min_linear_velocity_limit;
    max_angular_velocity_limit = config.max_angular_velocity_limit;
    min_angular_velocity_limit = config.min_angular_velocity_limit;
    use_sinh_law = config.use_sinh_law;
    use_tanh_law = config.use_tanh_law;
    backwards_docking = config.enable_backwards_docking;
}*/

bool OttoDocking::locker_door_opened_srv_cb(std_srvs::SetBool::Request &req, std_srvs::SetBool::Response &res)
{
    locker_door_opened_flag_ = req.data;
}

void OttoDocking::publish_marker_point(const geometry_msgs::PoseStamped &poseStamped, const std::string &ns, const std::vector<double> &color, double size)
{
    visualization_msgs::Marker marker;
    marker.header = poseStamped.header;
    marker.ns = ns;
    marker.id = 0;
    marker.type = visualization_msgs::Marker::ARROW; // Use ARROW type for visualization of orientation
    marker.action = visualization_msgs::Marker::ADD;
    marker.pose = poseStamped.pose;
    marker.scale.x = size * 2.0; // Adjust the scale for the arrow length
    marker.scale.y = size / 2.0; // Adjust the scale for the arrow width
    marker.scale.z = size / 2.0; // Adjust the scale for the arrow height
    marker.color.r = color[0];
    marker.color.g = color[1];
    marker.color.b = color[2];
    marker.color.a = color[3];
    marker.lifetime = ros::Duration(1); // Adjust the lifetime as needed

    // Use a Publisher to publish the marker
    marker_pub.publish(marker);
}

void OttoDocking::create_line_pose(const geometry_msgs::Pose &pose1, const geometry_msgs::Pose &pose2, const std_msgs::Header &header)
{
    // Extract position from PoseStamped messages
    geometry_msgs::Point position1 = pose1.position;
    geometry_msgs::Point position2 = pose2.position;

    // Convert positions to Eigen vectors
    double offset = backwards_docking ? marker_y_offset : -marker_y_offset;
    Eigen::Vector3d pose1_array(position1.x + offset, 0.0, position1.z);
    Eigen::Vector3d pose2_array(position2.x + offset, 0.0, position2.z);

    // Calculate LineVector
    Eigen::Vector3d line_vector = pose2_array - pose1_array;

    // Calculate Midpoint
    Eigen::Vector3d midpoint_array = (pose1_array + pose2_array) / 2.0;

    // Calculate Perpendicular Vector
    Eigen::Vector3d up_vector(0, -1, 0);
    Eigen::Vector3d perpendicular_vector = line_vector.cross(up_vector);

    // Normalize Perpendicular Vector
    perpendicular_vector.normalize();

    // Calculate Roll, Pitch, and Yaw angles based on the perpendicular vector
    double roll = 0.0; // Assuming no roll
    double pitch = std::asin(-perpendicular_vector[1]);
    double yaw = std::atan2(perpendicular_vector[2], perpendicular_vector[0]) + marker_yaw_offset;

    // Apply 180-degree rotation if backwards_docking is enabled
    if (!backwards_docking)
    {
        double current_yaw = std::atan2(perpendicular_vector[2], -perpendicular_vector[0]) + marker_yaw_offset;
        yaw += M_PI; // Add 180 degrees to the yaw angle
    }

    // Translate the midpoint 1 meter forward docking_distance = 0.8  undocking_distance = 2.0
    Eigen::Vector3d translated_point_1 = midpoint_array + (docking_distance + 0.15) * perpendicular_vector;
    if (is_lockerdocking_)
    {
        Eigen::Vector3d translated_point_2 = midpoint_array + (docking_distance + 0.7) * perpendicular_vector;
    }
    else
    {
        Eigen::Vector3d translated_point_2 = midpoint_array + (docking_distance + 0.3) * perpendicular_vector;
    }
    Eigen::Vector3d translated_point_2 = midpoint_array + (docking_distance + 0.7) * perpendicular_vector;
    Eigen::Vector3d translated_point_3 = midpoint_array + docking_distance * perpendicular_vector;
    Eigen::Vector3d translated_point_4 = midpoint_array + undocking_distance * perpendicular_vector;

    // Create a new PoseStamped for the projected point
    geometry_msgs::PoseStamped projected_pose_1, projected_pose_2, projected_pose_3, projected_pose_4;
    projected_pose_1.pose.position.x = translated_point_1[0];
    projected_pose_1.pose.position.y = translated_point_1[1];
    projected_pose_1.pose.position.z = translated_point_1[2];

    // Set orientation based on roll, pitch, and yaw
    tf2::Quaternion orientation_quaternion;
    orientation_quaternion.setRPY(roll, -yaw, pitch);
    projected_pose_1.pose.orientation = tf2::toMsg(orientation_quaternion);

    projected_pose_2.header = header;

    if (is_lockerdocking_)
    {
        projected_pose_2.pose.position.x = translated_point_2[0] - pose_x_offset_; //-0.40 // I added
    }
    else
    {
        projected_pose_2.pose.position.x = translated_point_2[0];
    }

    projected_pose_2.pose.position.y = translated_point_2[1];
    projected_pose_2.pose.position.z = translated_point_2[2];
    projected_pose_2.pose.orientation = projected_pose_1.pose.orientation;

    projected_pose_3.header = header;
    projected_pose_3.pose.position.x = translated_point_3[0];
    projected_pose_3.pose.position.y = translated_point_3[1];
    projected_pose_3.pose.position.z = translated_point_3[2];
    projected_pose_3.pose.orientation = projected_pose_1.pose.orientation;

    projected_pose_4.header = header;
    if (is_lockerdocking_)
    {
        projected_pose_4.pose.position.x = translated_point_4[0] - pose_x_offset_;
        // ROS_INFO_STREAM("projected pose 4.X =: " << projected_pose_4.pose.position.x);
    }
    else
    {
        projected_pose_4.pose.position.x = translated_point_4[0];
    }

    projected_pose_4.pose.position.y = translated_point_4[1];
    projected_pose_4.pose.position.z = translated_point_4[2];
    projected_pose_4.pose.orientation = projected_pose_1.pose.orientation;

    {
        boost::mutex::scoped_lock l{docking_target_pose_mutex};

        tf2::doTransform(projected_pose_3, dock_pose_contact, baseLinkNav_camera_tf);
        tf2::doTransform(projected_pose_1, dock_pose_1, baseLinkNav_camera_tf);
        tf2::doTransform(projected_pose_2, dock_pose_2, baseLinkNav_camera_tf);
        tf2::doTransform(projected_pose_4, undock_pose, baseLinkNav_camera_tf);
        // ROS_INFO_STREAM("Transformed pose: " << dock_pose_2);

        publish_marker_point(dock_pose_contact, "dockingPoseContact", {1.0, 1.0, 0.0, 1.0}, 0.1);
    }
}

void OttoDocking::get_virtual_docking_pose(const geometry_msgs::PoseStamped &pose_in_map_frame,
                                           geometry_msgs::PoseStamped &virtual_docking_pose)
{
    // convert pose from map frame to base_link_nav frame
    geometry_msgs::TransformStamped map_baseLinkNav_tf;
    bool got_map_baseLinkNav_tf = false;
    while (!got_map_baseLinkNav_tf)
    {
        try
        {
            map_baseLinkNav_tf = tf_buffer.lookupTransform("base_link_nav", pose_in_map_frame.header.frame_id, ros::Time(0));
            got_map_baseLinkNav_tf = true;
        }
        catch (tf2::TransformException &ex)
        {
            ROS_WARN("Error getting tf from base_link_nav to camera frame, error %s", ex.what());
            ros::Duration(0.02).sleep();
        }
    }

    tf2::doTransform(pose_in_map_frame, virtual_docking_pose, map_baseLinkNav_tf);
    publish_marker_point(virtual_docking_pose, "virtual_docking_pose", {1.0, 1.0, 0.0, 1.0}, 0.1);
}

bool OttoDocking::start_apriltag_detection(bool status)
{
    std::string gscam_service_name = "/" + camera_frame + "/publish_image_for_docking";
    // bool started_gscam = service_handler(status, gscam_service_name);
    bool started_gscam = true;

    std::string apriltag_service_name = "/apriltag_ros_continuous_node/enable_apriltag_detection";
    bool started_apriltag = service_handler(status, apriltag_service_name);

    return started_gscam && started_apriltag;
}

bool OttoDocking::hardware_start_communication(int status)
{
    for (int i = 0; i < 5; i++)
    {
        std::string service_name;
        if (is_lockerdocking_)
        {
            service_name = "/set_locker_docking_flags";
        }
        else
        {
            service_name = "/set_docking_flags";
        }
        // std::string service_name = "/set_docking_flags";
        ros::ServiceClient client = nh.serviceClient<ottonomy_msgs::SetDockingFlags>(service_name);
        ottonomy_msgs::SetDockingFlags srv;
        srv.request.name = "DOCK_BACK";
        srv.request.flag_num = station_id;
        srv.request.flag_val = status;
        print_and_publish_log("srv data " + srv.request.name + " " + std::to_string(srv.request.flag_num) + " " + std::to_string(srv.request.flag_val));
        if (!client.call(srv))
        {
            print_and_publish_log("Error: Unable to call " + service_name +
                                  " service success: " + std::to_string(srv.response.success));
            ros::Duration(2.0).sleep();
            continue;
        }
        else
        {
            if (srv.response.success != true)
            {
                print_and_publish_log("Error: Return false from service " + service_name +
                                      " service success: " + std::to_string(srv.response.success));
                ros::Duration(2.0).sleep();
                continue;
            }
            else
            {
                ROS_INFO("Service '%s' for start_communication %d call success", service_name.c_str(), status);
            }
        }

        // check if flag has changed
        ros::Time start_time = ros::Time::now();
        while (ros::Time::now().toSec() - start_time.toSec() < 5.0)
        {
            if (communication_flag_status_ == status)
            {
                return true;
            }

            ros::Duration(0.1).sleep();
        }

        ROS_INFO("start_communication current state %d", communication_flag_status_);
    }

    return false;
}

bool OttoDocking::hardware_start_charging(int status)
{
    for (int i = 0; i < 5; i++)
    {
        std::string service_name = "/set_docking_flags";
        ros::ServiceClient client = nh.serviceClient<ottonomy_msgs::SetDockingFlags>(service_name);
        ottonomy_msgs::SetDockingFlags srv;
        srv.request.name = "DOCK_BACK";
        srv.request.flag_num = 0;
        srv.request.flag_val = status;
        if (!client.call(srv))
        {
            print_and_publish_log("Error: Unable to call " + service_name +
                                  " service success: " + std::to_string(srv.response.success));
            ros::Duration(2.0).sleep();
            continue;
        }
        else
        {
            if (srv.response.success != true)
            {
                print_and_publish_log("Error: Return false from service " + service_name +
                                      " service success: " + std::to_string(srv.response.success));
                ros::Duration(2.0).sleep();
                continue;
            }
            else
            {
                ROS_INFO("Service '%s' for start_charging %d call success", service_name.c_str(), status);
            }
        }

        // check if flag has changed
        ros::Time start_time = ros::Time::now();
        while (ros::Time::now().toSec() - start_time.toSec() < 5.0)
        {
            if (charging_flag_status_ == status)
            {
                return true;
            }

            ros::Duration(0.1).sleep();
        }

        ROS_INFO("start_charging current state %d", charging_flag_status_);
    }

    return false;
}

void OttoDocking::robot_io_subscriber_callback(const ottonomy_msgs::RobotIOStates &robot_io_states)
{
    boost::mutex::scoped_lock l{hardware_feedback_mutex_};

    if ((int)robot_io_states.docking.size() > 0)
    {

        hardware_feedback_time_ = robot_io_states.docking[0].stamp;
        contact_force_1_buffer_.push_back((int)robot_io_states.docking[0].contact_force1);
        contact_force_2_buffer_.push_back((int)robot_io_states.docking[0].contact_force2);

        contact_force_1_average_ = std::accumulate(contact_force_1_buffer_.begin(), contact_force_1_buffer_.end(), 0.0) / contact_force_1_buffer_.size();
        contact_force_2_average_ = std::accumulate(contact_force_2_buffer_.begin(), contact_force_2_buffer_.end(), 0.0) / contact_force_2_buffer_.size();
        communication_flag_status_ = robot_io_states.docking[0].communication_status;
        charging_flag_status_ = robot_io_states.docking[0].charging_status;
    }

    if ((int)robot_io_states.locker_docking.size() > 0)
    {
        communication_flag_status_ = robot_io_states.locker_docking[0].communication_status;

        if (robot_io_states.locker_docking[0].cliff_sensor_1 == 0 || robot_io_states.locker_docking[0].cliff_sensor_1 == 999)
            return;

        if (robot_io_states.locker_docking[0].cliff_sensor_2 == 0 || robot_io_states.locker_docking[0].cliff_sensor_2 == 999)
            return;

        hardware_feedback_time_ = robot_io_states.locker_docking[0].stamp;

        cliff_sensor_1_buffer_.push_back((int)robot_io_states.locker_docking[0].cliff_sensor_1);
        cliff_sensor_2_buffer_.push_back((int)robot_io_states.locker_docking[0].cliff_sensor_2);

        cliff_sensor_1_average_ = std::accumulate(cliff_sensor_1_buffer_.begin(), cliff_sensor_1_buffer_.end(), 0.0) / cliff_sensor_1_buffer_.size();
        cliff_sensor_2_average_ = std::accumulate(cliff_sensor_2_buffer_.begin(), cliff_sensor_2_buffer_.end(), 0.0) / cliff_sensor_2_buffer_.size();
    }
}

void OttoDocking::robot_info_subscriber_callback(const ottonomy_msgs::RobotInfo &robot_info)
{
    boost::mutex::scoped_lock l{robot_info_mutex_};
    robot_info_ = robot_info;
}

void OttoDocking::motor_driver_1_feedback_callback(const ottonomy_msgs::MotorDriverFeedback &feedback)
{
    boost::mutex::scoped_lock l{motor_driver_feedback_mutex_};
    motor_driver_feedback_1_ = feedback;
}

void OttoDocking::motor_driver_2_feedback_callback(const ottonomy_msgs::MotorDriverFeedback &feedback)
{
    boost::mutex::scoped_lock l{motor_driver_feedback_mutex_};
    motor_driver_feedback_2_ = feedback;
}

bool OttoDocking::force_apply_brakes(bool status)
{
    std::string service_name = "/force_apply_brakes";
    return service_handler(status, service_name);
}

bool OttoDocking::docking_drive_mode(bool status)
{
    std::string service_name = "/ottonomy_robot_node/docking_drive_mode";
    return service_handler(status, service_name);
}

bool OttoDocking::reduce_back_safety(bool status)
{
    std::string service_name = "/in_docking";
    return service_handler(status, service_name);
}

bool OttoDocking::disengage_motors(bool status, const int max_retries)
{
    // int retries = 10;
    int retries = 0;
    bool success = false;
    while (!success)
    {
        std::string service_name = "/ottonomy_robot_node/disengage_motors";
        success = service_handler(status, service_name);

        retries++;
        if (retries > max_retries)
        {
            ROS_ERROR("Service '%s' call with data %d failed", service_name.c_str(), status);
            return false;
        }

        if (success)
        {
            // ensure that the robot is in auto run moving
            boost::mutex::scoped_lock l{robot_info_mutex_};

            if (status == false) // engage the motors
            {
                if (robot_info_.status != "AUTO_RUN_MOVING")
                {
                    success = false;
                }
            }
            else // disengage the motors
            {
                if (robot_info_.status == "AUTO_RUN_MOVING")
                {
                    success = false;
                }
            }
        }

        ros::Duration(0.5).sleep();
    }

    return true;
}

bool OttoDocking::service_handler(bool status, std::string service_name, const int max_retries)
{
    int retries = 0;
    bool success = false;
    while (!success)
    {
        ros::ServiceClient client = nh.serviceClient<std_srvs::SetBool>(service_name);
        std_srvs::SetBool srv;
        srv.request.data = status;
        if (!client.call(srv))
        {
            print_and_publish_log("Error: Unable to call " + service_name +
                                  " service success: " + std::to_string(srv.response.success) +
                                  " message: " + srv.response.message);
            // ros::Duration(0.5).sleep();
            ros::Duration(2.0).sleep();
            retries++;
        }
        else
        {
            if (srv.response.success != true)
            {
                print_and_publish_log("Error: Return false from service " + service_name +
                                      " service success: " + std::to_string(srv.response.success) +
                                      " message: " + srv.response.message);
                // ros::Duration(0.5).sleep();
                ros::Duration(2.0).sleep();
                retries++;
            }
            else
            {
                ROS_INFO("Service '%s' call success", service_name.c_str());
                break;
            }
        }

        if (retries > max_retries)
        {
            ROS_ERROR("Service '%s' call with data %d failed", service_name.c_str(), status);
            // return true;
            return false;
        }
    }

    return true;
}

void OttoDocking::detection_callback(const apriltag_ros::AprilTagDetectionArray &marker)
{
    if (docking_state == DOCKING_STATE::INIT)
    {
        return;
    }
    if (docking_state == DOCKING_STATE::VIRTUALDOCKING)
    {
        return;
    }

    if (marker.detections.size() != 2)
    {
        ROS_INFO_THROTTLE(3, "Need exactly 2 April Tags for docking!");
        return;
    }

    target_detection_time = marker.header.stamp;
    geometry_msgs::Pose poseTag1 = marker.detections[1].pose.pose.pose;
    geometry_msgs::Pose poseTag2 = marker.detections[0].pose.pose.pose;

    if (docking_type == DOCKING_TYPE::LOCKERDOCKING || docking_type == DOCKING_TYPE::LOCKERUNDOCKING)
    {
        is_lockerdocking_ = true;
    }
    else
    {
        is_lockerdocking_ = false;
    }

    create_line_pose(poseTag1, poseTag2, marker.header);
}

double OttoDocking::calculate_yaw_error(const geometry_msgs::Pose &pose1)
{
    tf2::Quaternion quaternion1(pose1.orientation.x, pose1.orientation.y, pose1.orientation.z, pose1.orientation.w);
    tf2::Matrix3x3 mat(quaternion1);
    double roll, pitch, yaw;
    mat.getRPY(roll, pitch, yaw);
    return yaw;
}

void OttoDocking::calc_error_given_target_pose(const geometry_msgs::PoseStamped target_pose,
                                               float &error_x, float &error_y, float &error_yaw)
{
    boost::mutex::scoped_lock l{docking_target_pose_mutex};
    double backwards_docking_factor = (backwards_docking) ? -1.0 : 1.0;

    error_x = target_pose.pose.position.x * backwards_docking_factor;
    error_y = target_pose.pose.position.y;
    error_yaw = calculate_yaw_error(target_pose.pose);

    publish_marker_point(target_pose, "docking_marker", {1.0, 0.0, 0.0, 1.0}, 0.1);
}

void OttoDocking::calc_valid_vel_given_error(float error_x, float error_y, float error_yaw,
                                             float &Vx, float &Vy, float &W)
{
    bool valid_velocities_generated = false;
    float backwards_docking_factor = (backwards_docking) ? -1.0 : 1.0;

    while (!valid_velocities_generated)
    {
        // calculate velocities for the error
        if (use_sinh_law)
        {
            Vx = max_linear_velocity_limit * sinh(kp_x * error_x / max_linear_velocity_limit) * backwards_docking_factor;
            Vy = max_linear_velocity_limit * sinh(kp_y * error_y / max_linear_velocity_limit);
            W = max_angular_velocity_limit * sinh(kp_yaw * error_yaw / max_angular_velocity_limit);
        }
        else if (use_tanh_law)
        {
            Vx = max_linear_velocity_limit * tanh(kp_x * error_x / max_linear_velocity_limit) * backwards_docking_factor;
            Vy = max_linear_velocity_limit * tanh(kp_y * error_y / max_linear_velocity_limit);
            W = max_angular_velocity_limit * tanh(kp_yaw * error_yaw / max_angular_velocity_limit);
        }
        // else if(use_purepursuit) //NOT BEING USED
        // {
        //     double y_r = error_y;
        //     double x_r = error_x;
        //     double lookahead_distance = 0.2;
        //     double gain = 0.5;
        //     double L_full = std::sqrt((error_x * error_x) + (error_y * error_y));

        //     if (L_full > lookahead_distance) {
        //         double scale = lookahead_distance / L_full;
        //         x_r *= scale;
        //         y_r *= scale;

        //         double intermediate_L = std::sqrt((x_r * x_r) + (y_r * y_r));
        //         double curvature = (2.0 * y_r) / (intermediate_L * intermediate_L);
        //         Vx = max_linear_velocity_limit / (1.0 + gain * fabs(curvature));
        //         W = Vx * curvature;
        //         Vy = 0.0;
        //     }
        //     else
        //     {
        //         double curvature_ = (2.0 * error_y) / (L_full * L_full);
        //         Vx = max_linear_velocity_limit / (1.0 + gain * fabs(curvature_));
        //         W = Vx * curvature_;
        //         Vy = 0.0;
        //         // x_r = 0.0;
        //         // y_r = 0.0;
        //     }

        // }
        else
        {
            Vx = kp_x * error_x;
            Vy = kp_y * error_y;
            W = kp_yaw * error_yaw;
        }

        // apply velocity limits on velocities generated
        if (Vx > 0)
        {
            Vx = std::min(Vx, max_linear_velocity_limit);
            Vx = std::max(Vx, min_linear_velocity_limit);
        }
        else if (Vx < 0)
        {
            Vx = std::max(Vx, -max_linear_velocity_limit);
            Vx = std::min(Vx, -min_linear_velocity_limit);
        }
        else
        {
            Vx = 0.0;
        }

        if (Vy > 0)
        {
            Vy = std::min(Vy, max_linear_velocity_limit);
            // Vy = std::max(Vy, min_linear_velocity_limit);
            // if (fabs(Vy) < min_linear_velocity_limit)
            //     Vy = 0.0;
        }
        else if (Vy < 0)
        {
            Vy = std::max(Vy, -max_linear_velocity_limit);
            // Vy = std::min(Vy, -min_linear_velocity_limit);
            // if (fabs(Vy) < min_linear_velocity_limit)
            //     Vy = 0.0;
        }
        else
        {
            Vy = 0.0;
        }

        if (W > 0)
        {
            W = std::min(W, max_angular_velocity_limit);
            // W = std::max(W, min_angular_velocity_limit);
            // if (fabs(W) < min_angular_velocity_limit)
            //     W = 0.0;
        }
        else if (W < 0)
        {
            W = std::max(W, -max_angular_velocity_limit);
            // W = std::min(W, -min_angular_velocity_limit);
            // if (fabs(W) < min_angular_velocity_limit)
            //     W = 0.0;
        }
        else
        {
            W = 0.0;
        }

        // check if velocities generated are valid
        bool valid_velocities_generated = check_if_velocities_are_valid(Vx, Vy, W);
        if (valid_velocities_generated)
        {
            valid_velocities_generated = true;
            break;
        }
        else
        {
            error_y *= 0.9;
            error_yaw *= 0.9;
        }
    }
}

bool OttoDocking::check_if_velocities_are_valid(const double V, const double theta, double W_in)
{
    if (robot_type_ != "ackermann_4steer")
        return true;

    bool straight_steering_when_stopping_ = true;
    double steer_max_cmd = 1800;
    double position_scaling_factor = 4864;
    double max_steer_angle = steer_max_cmd * M_PI / position_scaling_factor;
    double ROBOT_LENGTH = 0.685;
    double ROBOT_WIDTH = 0.425;
    double FRONT_OFFSET_DISTANCE = 0.070;
    double Rx_in = ROBOT_LENGTH / 2.0;
    double Ry_in = ROBOT_WIDTH / 2.0;
    double OD_in = FRONT_OFFSET_DISTANCE;
    double vel_pos[8] = {0.0};
    int S_FL = 0, V_FL = 1, S_FR = 2, V_FR = 3, S_RL = 4, V_RL = 5, S_RR = 6, V_RR = 7;

    double Vx_in = V;
    double Vy_in = theta;
    if (V < 0.0)
    {
        W_in *= -1;
        Vy_in *= -1;
    }

    double Vx, Vy, Rx, Ry;
    int dir = Vx_in / fabs(Vx_in);
    Vx = dir * Vx_in - W_in * Ry_in;
    Vy = Vy_in + W_in * Rx_in;
    vel_pos[S_FL] = atan2(Vy, Vx);

    Vx = dir * Vx_in + W_in * Ry_in;
    Vy = Vy_in + W_in * Rx_in;
    vel_pos[S_FR] = atan2(Vy, Vx);

    Vx = dir * Vx_in - W_in * Ry_in;
    Vy = Vy_in - W_in * Rx_in;
    vel_pos[S_RL] = atan2(Vy, Vx);

    Vx = dir * Vx_in + W_in * Ry_in;
    Vy = Vy_in - W_in * Rx_in;
    vel_pos[S_RR] = atan2(Vy, Vx);

    if (fabs(vel_pos[S_FL]) < max_steer_angle && fabs(vel_pos[S_FR]) < max_steer_angle &&
        fabs(vel_pos[S_RL]) < max_steer_angle && fabs(vel_pos[S_RR]) < max_steer_angle)
    {
        return true;
    }
    else
    {
        // ROS_WARN("Invalid input commands, swerve steering command out of range");
        return false;
    }
}

inline void OttoDocking::print_and_publish_log(const std::string &msg)
{
    ROS_INFO("%s", msg.c_str());

    ottonomy_msgs::OttoDockFeedback feedback;
    feedback.id = docking_id;
    feedback.status = msg;
    dockAS->publishFeedback(feedback);
}

inline void OttoDocking::publish_velocity(const float Vx, const float Vy, const float W)
{
    geometry_msgs::Twist cmd_vel;
    cmd_vel.linear.x = Vx;
    cmd_vel.linear.y = Vy;
    cmd_vel.angular.z = W;
    cmd_vel_pub.publish(cmd_vel);
}

bool OttoDocking::move_to_location(const geometry_msgs::PoseStamped &target_pose, const uint16_t time_limit_sec,
                                   const float error_threshold_xy, const float error_threshold_yaw,
                                   const bool move_only_in_x, bool &target_lost, bool &prempt_requested)
{
    // initialize the arguments required
    ros::Time start_time = ros::Time::now();
    target_lost = false;
    prempt_requested = false;
    ros::Rate rate(20.0);

    while (true)
    {
        // check if target is lost
        float delay_in_processing_pose = fabs(ros::Time::now().toSec() - target_detection_time.toSec());
        if (docking_state == DOCKING_STATE::VIRTUALDOCKING)
        {
            delay_in_processing_pose = 0.0;
        }
        else if (communicate_with_dock_ == true &&
                 (docking_state == DOCKING_STATE::MOVETO_CONTACT || docking_state == DOCKING_STATE::DOCKING_WITH_CLIFF ||
                  docking_state == DOCKING_STATE::UNDOCKING_WITH_CLIFF))
        {
            if (use_motor_current_instead_of_contact_force_)
            {
                boost::mutex::scoped_lock l{motor_driver_feedback_mutex_};
                delay_in_processing_pose = std::max(fabs(ros::Time::now().toSec() - motor_driver_feedback_1_.header.stamp.toSec()),
                                                    fabs(ros::Time::now().toSec() - motor_driver_feedback_2_.header.stamp.toSec()));
            }
            else
            {
                boost::mutex::scoped_lock l{hardware_feedback_mutex_};
                delay_in_processing_pose = fabs(ros::Time::now().toSec() - hardware_feedback_time_.toSec());
            }
        }

        if (delay_in_processing_pose > target_lost_time_for_recovery)
        {
            print_and_publish_log("returning from move_to_location since too much delay [" +
                                  std::to_string(delay_in_processing_pose) + "] in processing pose");

            publish_velocity(0.0, 0.0, 0.0);

            target_lost = true;
            return false;
        }
        else if (delay_in_processing_pose > max_target_lost_time_for_moving_robot)
        {
            print_and_publish_log("stopping the robot since too much delay [" +
                                  std::to_string(delay_in_processing_pose) + "] in processing pose");

            publish_velocity(0.0, 0.0, 0.0);

            rate.sleep();
            continue;
        }

        // check if prempt is requested
        if (dockAS->isPreemptRequested())
        {
            print_and_publish_log("exiting move_to_location since prempt request received");
            publish_velocity(0.0, 0.0, 0.0);

            prempt_requested = true;
            return false;
        }

        // check if time limit exceeded
        if (ros::Time::now().toSec() - start_time.toSec() > time_limit_sec)
        {
            print_and_publish_log("exiting move_to_location since execution time limit exceeded");
            publish_velocity(0.0, 0.0, 0.0);

            return false;
        }

        // calculate the error for the given target location
        float error_x, error_y, error_yaw;
        if (docking_state == DOCKING_STATE::VIRTUALDOCKING)
        {
            geometry_msgs::PoseStamped virtual_docking_pose;
            get_virtual_docking_pose(target_pose, virtual_docking_pose);
            calc_error_given_target_pose(virtual_docking_pose, error_x, error_y, error_yaw);
        }
        else
        {
            // for docking and undocking, pose is being updated in marker callback, and pose is passed as a reference
            calc_error_given_target_pose(target_pose, error_x, error_y, error_yaw);
        }

        // check if target is reached
        if ((fabs(error_x) < error_threshold_xy) &&
            (fabs(error_y) < error_threshold_xy) &&
            (fabs(error_yaw) < error_threshold_yaw) && (docking_state != DOCKING_STATE::DOCKING_WITH_CLIFF && docking_state != DOCKING_STATE::UNDOCKING_WITH_CLIFF))
        {
            print_and_publish_log("move_to_location target reached error_x: " + std::to_string(error_x) +
                                  " error_y: " + std::to_string(error_y) +
                                  " error_yaw: " + std::to_string(error_yaw));

            // stop the robot
            publish_velocity(0.0, 0.0, 0.0);
            return true;
        }
        else if (move_only_in_x && (fabs(error_x) < error_threshold_xy) & (docking_state != DOCKING_STATE::DOCKING_WITH_CLIFF && docking_state != DOCKING_STATE::UNDOCKING_WITH_CLIFF))
        {
            print_and_publish_log("move_to_location target reached error_x: " + std::to_string(error_x) +
                                  " error_y: " + std::to_string(error_y) +
                                  " error_yaw: " + std::to_string(error_yaw));

            // stop the robot
            publish_velocity(0.0, 0.0, 0.0);
            return true;
        }
        else if (communicate_with_dock_ == true && docking_state == DOCKING_STATE::MOVETO_CONTACT && !is_lockerdocking_)
        {
            bool contact_force_reached = false;
            if (use_motor_current_instead_of_contact_force_)
            {
                boost::mutex::scoped_lock l{motor_driver_feedback_mutex_};
                if ((motor_driver_feedback_1_.current[0] + motor_driver_feedback_1_.current[1] +
                     motor_driver_feedback_2_.current[0] + motor_driver_feedback_2_.current[1]) > motor_amps_threshold_)
                {
                    contact_force_reached = true;
                    print_and_publish_log("Motor current > threshold " +
                                          std::to_string(motor_driver_feedback_1_.current[0]) +
                                          " " + std::to_string(motor_driver_feedback_1_.current[1]) +
                                          " " + std::to_string(motor_driver_feedback_2_.current[0]) +
                                          " " + std::to_string(motor_driver_feedback_2_.current[1]));
                }
            }
            else
            {
                boost::mutex::scoped_lock l{hardware_feedback_mutex_};
                if (((contact_force_1_average_ > contact_force_threshold_) || (contact_force_2_average_ > contact_force_threshold_)) &&
                    ((contact_force_1_average_ > min_contact_force_threshold_) && (contact_force_2_average_ > min_contact_force_threshold_)))
                {
                    contact_force_reached = true;

                    print_and_publish_log("Contact force > threshold " +
                                          std::to_string(contact_force_1_average_) + " " + std::to_string(contact_force_2_average_));
                }
            }

            if (contact_force_reached)
            {
                print_and_publish_log("move_to_location target reached error_x: " + std::to_string(error_x) +
                                      " error_y: " + std::to_string(error_y) +
                                      " error_yaw: " + std::to_string(error_yaw));

                // force apply brakes
                // force_apply_brakes(true);
                docking_drive_mode(true);

                // stop the robot
                publish_velocity(0.0, 0.0, 0.0);
                ros::Duration(1.0).sleep();

                // so that when next velocity is recived, brakes will auto remove
                // force_apply_brakes(false);
                docking_drive_mode(false);

                return true;
            }
        }
        else if (communicate_with_dock_ == true && docking_state == DOCKING_STATE::DOCKING_WITH_CLIFF && is_lockerdocking_)
        {
            bool contact_force_reached = false;

            print_and_publish_log("cliff_sensor_1_average_ : " + std::to_string(cliff_sensor_1_average_) + " cliff_sensor_2_average_ : " + std::to_string(cliff_sensor_2_average_));

            boost::mutex::scoped_lock l{hardware_feedback_mutex_};
            if (((cliff_sensor_1_average_ < min_cliff_sensor_docking_threshold_) || (cliff_sensor_2_average_ < min_cliff_sensor_docking_threshold_)))
            {
                contact_force_reached = true;
                publish_velocity(0.0, 0.0, 0.0);
                print_and_publish_log("cliff_sensor data < threshold " +
                                      std::to_string(cliff_sensor_1_average_) + " " + std::to_string(cliff_sensor_2_average_));
            }
            // }

            if (contact_force_reached)
            {
                print_and_publish_log("move_to_location target reached error_x: " + std::to_string(error_x) +
                                      " error_y: " + std::to_string(error_y) +
                                      " error_yaw: " + std::to_string(error_yaw));
                // stop the robot
                publish_velocity(0.0, 0.0, 0.0);
                ros::Duration(1.0).sleep();
                return true;
            }
        }
        else if (communicate_with_dock_ == true && docking_state == DOCKING_STATE::UNDOCKING_WITH_CLIFF && is_lockerdocking_)
        {
            bool contact_force_reached = false;

            print_and_publish_log("cliff_sensor_1_average_ : " + std::to_string(cliff_sensor_1_average_) + " cliff_sensor_2_average_ : " + std::to_string(cliff_sensor_2_average_));

            boost::mutex::scoped_lock l{hardware_feedback_mutex_};
            if (((cliff_sensor_1_average_ > min_cliff_sensor_undocking_threshold_) || (cliff_sensor_2_average_ > min_cliff_sensor_undocking_threshold_)))
            {
                contact_force_reached = true;

                print_and_publish_log("cliff_sensor data < threshold " +
                                      std::to_string(cliff_sensor_1_average_) + " " + std::to_string(cliff_sensor_2_average_));
            }
            // }

            if (contact_force_reached)
            {
                print_and_publish_log("move_to_location target reached error_x: " + std::to_string(error_x) +
                                      " error_y: " + std::to_string(error_y) +
                                      " error_yaw: " + std::to_string(error_yaw));
                // stop the robot
                publish_velocity(0.0, 0.0, 0.0);
                ros::Duration(1.0).sleep();
                return true;
            }
        }

        // compute valid velocity and move the robot to target location
        float Vx, Vy, W;
        calc_valid_vel_given_error(error_x, error_y, error_yaw, Vx, Vy, W);

        if (communicate_with_dock_ == true && docking_state == DOCKING_STATE::DOCKING_WITH_CLIFF && is_lockerdocking_)
            Vx = 0.05 * -1;

        if (communicate_with_dock_ == true && docking_state == DOCKING_STATE::UNDOCKING_WITH_CLIFF && is_lockerdocking_)
            Vx = 0.05;

        ottonomy_msgs::DockingState docking_state;
        docking_state.error_x = error_x;
        docking_state.error_y = error_y;
        docking_state.error_yaw = error_yaw;
        docking_state.vel_lin_x = Vx;

        if (move_only_in_x)
        {
            publish_velocity(Vx, 0.0, 0.0);
            docking_state.vel_lin_y = 0.0;
            docking_state.vel_ang_z = 0.0;
        }
        else
        {
            publish_velocity(Vx, Vy, W);
            docking_state.vel_lin_y = Vy;
            docking_state.vel_ang_z = W;
        }

        docking_state.delay_in_processing_pose = delay_in_processing_pose;
        docking_state_pub.publish(docking_state);

        rate.sleep();
    }

    return false;
}

void OttoDocking::publish_docking_status(const ros::TimerEvent&)
{
    ottonomy_msgs::DockingState docking_state_msg;
    docking_state_msg.docking_state = docking_state;
    docking_state_msg.docking_state_str = docking_state_to_string[docking_state];
    docking_state_msg.docking_type =  docking_type_to_string[docking_type];
    docking_state_msg.message = current_message_;
    current_docking_state_pub_.publish(docking_state_msg);
}

void OttoDocking::execute_cb(const ottonomy_msgs::OttoDockGoalConstPtr &otto_docking)
{
    ROS_INFO("In execute_cb, received a new docking goal \ngoal_type: %s \ngoal_id: %s \nstation_id: %s",
             otto_docking->type.c_str(), otto_docking->id.c_str(), otto_docking->station_id.c_str());

    num_retries = 0;
    goal_abort_reason_ = "";
    station_id = -1;
    docking_id = otto_docking->id;

    if (otto_docking->type == "virtualdocking")
        station_id = 0;
    else
    {
        try
        {
            station_id = std::stoi(otto_docking->station_id);
        }
        catch (...)
        {
            ros::Duration(1.0).sleep();
            goal_abort_reason_ = "Rejecting goal, unable to parse station id: " + otto_docking->station_id;
            print_and_publish_log(goal_abort_reason_);
            docking_state = DOCKING_STATE::DOCKING_FAILED;
        }
    }

    if (station_id != -1)
    {
        is_lockerdocking_ = false;
        if (otto_docking->type == "docking")
        {
            docking_type = DOCKING_TYPE::DOCKING;
            docking_state = DOCKING_STATE::VIRTUALDOCKING;
        }
        else if (otto_docking->type == "manualdocking")
        {
            docking_type = DOCKING_TYPE::MANUALDOCKING;
            docking_state = DOCKING_STATE::START_APRILTAG;
        }
        else if (otto_docking->type == "virtualdocking")
        {
            docking_type = DOCKING_TYPE::VIRTUALDOCKING_TYPE;
            docking_state = DOCKING_STATE::VIRTUALDOCKING;
        }
        else if (otto_docking->type == "undocking")
        {
            docking_type = DOCKING_TYPE::UNDOCKING_TYPE;
            docking_state = DOCKING_STATE::START_APRILTAG;
        }
        else if (otto_docking->type == "lockerdocking")
        {
            docking_type = DOCKING_TYPE::LOCKERDOCKING;
            docking_state = DOCKING_STATE::START_APRILTAG;
            locker_door_opened_flag_ = false;
            is_lockerdocking_ = true;
        }
        else if (otto_docking->type == "lockerundocking")
        {
            docking_type = DOCKING_TYPE::LOCKERUNDOCKING;
            docking_state = DOCKING_STATE::START_APRILTAG;
            locker_door_opened_flag_ = true;
            is_lockerdocking_ = true;
        }
        else
        {
            ros::Duration(1.0).sleep();
            goal_abort_reason_ = "Rejecting goal, invalid docking_type: " + docking_type;
            print_and_publish_log(goal_abort_reason_);
            docking_state = DOCKING_STATE::DOCKING_FAILED;
        }
    }

    // run the docking state machine
    while (true)
    {
        // override current state if termination condition reached
        if (num_retries > max_docking_retries)
        {
            current_message_ =  "Docking failed: Max number of retries reached";
            print_and_publish_log(goal_abort_reason_);
            docking_state = DOCKING_STATE::DOCKING_FAILED;
        }

        if (dockAS->isPreemptRequested())
        {
            docking_state = DOCKING_STATE::DOCKING_PREMPTED;
        }

        switch (docking_state)
        {
        case DOCKING_STATE::VIRTUALDOCKING:
        {
            current_message_ =  "In VIRTUALDOCKING state";
            print_and_publish_log(">> entered VIRTUALDOCKING state");

            // get the virtual docking pose in base link nav frame
            geometry_msgs::PoseStamped virtual_docking_pose;
            get_virtual_docking_pose(otto_docking->target_pose, virtual_docking_pose);

            float error_x, error_y, error_yaw;
            calc_error_given_target_pose(virtual_docking_pose, error_x, error_y, error_yaw);

            // reject goal if virtual docking pose is too far away from robot location
            if (fabs(error_x) > 4.0 || fabs(error_y) > 4.0 || fabs(error_yaw) > 2.0)
            {
                goal_abort_reason_ = "Rejecting the goal since error in virtual docking too much, error_x, error_y, error_yaw: " +
                                     std::to_string(error_x) + " " +
                                     std::to_string(error_y) + " " +
                                     std::to_string(error_yaw);
                print_and_publish_log(goal_abort_reason_);
                docking_state = DOCKING_STATE::DOCKING_FAILED;
                break;
            }

            // move to location
            bool target_lost = false;
            bool prempt_requested = false;
            bool reached_location = move_to_location(otto_docking->target_pose,
                                                     max_action_time_sec,
                                                     error_threshold_virtualdocking_xy,
                                                     error_threshold_virtualdocking_yaw,
                                                     false,
                                                     target_lost,
                                                     prempt_requested);

            if (reached_location)
            {
                if (docking_type == DOCKING_TYPE::VIRTUALDOCKING_TYPE || simulate_)
                {
                    docking_state = DOCKING_STATE::DOCKING_COMPLETED;
                    break;
                }
                else
                {
                    docking_state = DOCKING_STATE::START_APRILTAG;
                    break;
                }
            }
            else
            {
                num_retries++;
                print_and_publish_log(">> Staying in virtual_docking state and retrying");
            }

            break;
        }

        case DOCKING_STATE::START_APRILTAG:
        {
            print_and_publish_log(">> entered START_APRILTAG state");
            current_message_ =  "In START_APRILTAG state";

            if (docking_type == DOCKING_TYPE::UNDOCKING_TYPE)
            {
                if (simulate_)
                {
                    std::string charge_service_name = "/set_charging";
                    bool started_gscam = service_handler(false, charge_service_name);
                    docking_state = DOCKING_STATE::DOCKING_COMPLETED;
                    break;
                }
            }

            if (start_apriltag_detection(true) && reduce_back_safety(true))
            {
                if (docking_type == DOCKING_TYPE::DOCKING || docking_type == DOCKING_TYPE::MANUALDOCKING)
                {
                    docking_state = DOCKING_STATE::MOVETO_POSE_2;
                }
                else if (docking_type == DOCKING_TYPE::UNDOCKING_TYPE)
                {
                    docking_state = DOCKING_STATE::HARDWARE_STOP_COMM_CHARGING;
                }
                else if (docking_type == DOCKING_TYPE::LOCKERDOCKING)
                {
                    docking_state = DOCKING_STATE::WAITING_FOR_LOCKER_DOOR_OPEN;
                }
                else if (docking_type == DOCKING_TYPE::LOCKERUNDOCKING)
                {
                    docking_state = DOCKING_STATE::HARDWARE_START_COMM;
                }
                break;
            }
            else
            {
                goal_abort_reason_ = "Unable to start gscam / apriltag or reduce back safety";
                print_and_publish_log(goal_abort_reason_);
                docking_state = DOCKING_STATE::DOCKING_FAILED;
                break;
            }

            break;
        }

        case DOCKING_STATE::WAITING_FOR_LOCKER_DOOR_OPEN:
        {
            current_message_ = "Waiting for the Locker Cabin Open Feedback";

            ros::Rate rate(1.0);
            double start_time = ros::Time::now().toSec();
            bool prempt_requested = false;
            while (true) // rotate recovery for max 1.5 min
            {
                if (dockAS->isPreemptRequested())
                {
                    publish_velocity(0.0, 0.0, 0.0);
                    prempt_requested = true;
                    break;
                }
                else if (locker_door_opened_flag_ || locker_door_always_opened_flag_)
                {
                    docking_state = DOCKING_STATE::MOVETO_POSE_2;
                    break;
                }
                else if (fabs(ros::Time::now().toSec() - start_time) > 180)
                {
                    goal_abort_reason_ = "Not able to open the locker door";
                    print_and_publish_log(goal_abort_reason_);
                    docking_state = DOCKING_STATE::DOCKING_FAILED;
                    // current_message_ = "Failed to recevied Locker Door Open Feedback";
                    break;
                }
                print_and_publish_log("Waiting for locker door to get open");
                rate.sleep();
            }
        }
        break;
        case DOCKING_STATE::STOP_APRILTAG:
        {
            print_and_publish_log(">> entered STOP_APRILTAG state");

            current_message_ = "In STOP_APRILTAG state";

            if (start_apriltag_detection(false) && reduce_back_safety(false))
            {
                docking_state = DOCKING_STATE::DOCKING_COMPLETED;
                break;
            }
            else
            {
                goal_abort_reason_ = "Unable to stop gscam / apriltag or stop reducing back safety";
                // current_message_ = "Unable to stop gscam / apriltag or stop reducing back safety";
                print_and_publish_log(goal_abort_reason_);
                docking_state = DOCKING_STATE::DOCKING_FAILED;
                break;
            }

            break;
        }

        case DOCKING_STATE::MOVETO_POSE_2:
        {
            print_and_publish_log(">> entered MOVETO_POSE_2 state");

            current_message_ = "In MOVETO_POSE_2 state";


            bool target_lost = false;
            bool prempt_requested = false;
            bool reached_location = move_to_location(dock_pose_2,
                                                     max_action_time_sec,
                                                     error_threshold_xy * 0.75,
                                                     error_threshold_yaw * 0.75,
                                                     false,
                                                     target_lost,
                                                     prempt_requested);

            if (reached_location)
            {
                print_and_publish_log("sleep for 1.0 sec");
                ros::Duration(1.0).sleep();

                if (communicate_with_dock_)
                {
                    docking_state = DOCKING_STATE::HARDWARE_START_COMM;
                }
                else
                {
                    docking_state = DOCKING_STATE::MOVETO_POSE_1;
                }
                break;
            }
            else
            {
                num_retries++;

                if (target_lost)
                {
                    previous_docking_state = DOCKING_STATE::MOVETO_POSE_2;
                    docking_state = DOCKING_STATE::ROTATE_RECOVERY;
                    break;
                }
                else
                {
                    print_and_publish_log(">> Staing in virtual_docking state and retrying!");
                }
            }

            break;
        }

        case DOCKING_STATE::HARDWARE_START_COMM:
        {
            print_and_publish_log(">> entered HARDWARE_START_COMM state");

            current_message_ = "In HARDWARE_START_COMM state";

            if (hardware_start_communication(1))
            {
                if (docking_type == DOCKING_TYPE::LOCKERUNDOCKING)
                {
                    docking_state = DOCKING_STATE::UNDOCKING_WITH_CLIFF;
                }
                else
                    docking_state = DOCKING_STATE::MOVETO_POSE_1;
                break;
            }
            else
            {
                goal_abort_reason_ = "Unable to start dock communication";
                // current_message_ = "Unable to start dock communication";
                print_and_publish_log(goal_abort_reason_);
                docking_state = DOCKING_STATE::DOCKING_FAILED;
                break;
            }

            break;
        }

        case DOCKING_STATE::MOVETO_POSE_1:
        {
            print_and_publish_log(">> entered MOVETO_POSE_1 state");

            current_message_ = "In MOVETO_POSE_1 state";

            bool target_lost = false;
            bool prempt_requested = false;
            bool reached_location = move_to_location(dock_pose_1,
                                                     max_action_time_sec,
                                                     error_threshold_xy * 0.75,
                                                     error_threshold_yaw * 0.75,
                                                     false,
                                                     target_lost,
                                                     prempt_requested);

            // check if y and yaw are also reached
            bool all_position_reached = false;
            float error_x, error_y, error_yaw;
            calc_error_given_target_pose(dock_pose_1, error_x, error_y, error_yaw);
            if (fabs(error_x) < 0.03 || fabs(error_y) < 0.03 || fabs(error_yaw) < 0.04)
            {
                all_position_reached = true;
            }

            if (reached_location && all_position_reached)
            {
                // print_and_publish_log("sleep for 1.0 sec");
                // ros::Duration(1.0).sleep();

                if (!is_lockerdocking_)
                    docking_state = DOCKING_STATE::MOVETO_CONTACT; // STOP_APRILTAG;for locker docking testing  MOVETO_CONTACT is original
                else
                    docking_state = DOCKING_STATE::DOCKING_WITH_CLIFF;
                break;
            }
            else
            {
                num_retries++;

                if (target_lost)
                {
                    goal_abort_reason_ = "Apriltag target lost";
                    // current_message_ = "Apriltag target lost";
                    print_and_publish_log(goal_abort_reason_);
                    docking_state = DOCKING_STATE::DOCKING_FAILED;
                    break;
                }
                else
                {
                    print_and_publish_log("Going back to MOVETO_POSE_2 since all positions not reached");
                    docking_state = DOCKING_STATE::MOVETO_POSE_2;
                    break;
                }
            }

            break;
        }

        case DOCKING_STATE::UNDOCKING_WITH_CLIFF:
        {
            print_and_publish_log(">> entered UNDOCKING_WITH_CLIFF state");

            current_message_ = "In UNDOCKING_WITH_CLIFF state";

            bool target_lost = false;
            bool prempt_requested = false;
            bool reached_location = move_to_location(undock_pose,
                                                     max_action_time_sec,
                                                     error_threshold_xy * 0.75,
                                                     error_threshold_yaw * 0.75,
                                                     true,
                                                     target_lost,
                                                     prempt_requested);

            if (reached_location)
            {
                if (communicate_with_dock_)
                {
                    hardware_start_communication(0);
                    docking_state = DOCKING_STATE::UNDOCKING;
                }
                break;
            }
            else
            {
                num_retries++;

                if (target_lost)
                {
                    goal_abort_reason_ = "Apriltag target lost";
                    // current_message_ = "Apriltag target lost";
                    print_and_publish_log(goal_abort_reason_);
                    docking_state = DOCKING_STATE::DOCKING_FAILED;
                    break;
                }
            }

            break;
        }

        case DOCKING_STATE::DOCKING_WITH_CLIFF:
        {
            print_and_publish_log(">> entered DOCKING_WITH_CLIFF state");

            current_message_ = "In DOCKING_WITH_CLIFF state";

            bool target_lost = false;
            bool prempt_requested = false;
            bool reached_location = move_to_location(dock_pose_contact,
                                                     max_action_time_sec,
                                                     error_threshold_xy * 0.75,
                                                     error_threshold_yaw * 0.75,
                                                     true,
                                                     target_lost,
                                                     prempt_requested);

            if (reached_location)
            {
                if (communicate_with_dock_)
                {
                    hardware_start_communication(0);
                    docking_state = DOCKING_STATE::STOP_APRILTAG;
                }
                break;
            }
            else
            {
                num_retries++;

                if (target_lost)
                {
                    goal_abort_reason_ = "Apriltag target lost";
                    // current_message_ = "Apriltag target lost";
                    print_and_publish_log(goal_abort_reason_);
                    docking_state = DOCKING_STATE::DOCKING_FAILED;
                    break;
                }
                else
                {
                    print_and_publish_log("Going back to move_to_location_2 since all positions not reached");
                    docking_state = DOCKING_STATE::MOVETO_POSE_2;
                    break;
                }
            }

            break;
        }

        case DOCKING_STATE::MOVETO_CONTACT:
        {
            print_and_publish_log(">> entered MOVETO_CONTACT state");

            current_message_ = "In MOVETO_CONTACT state";

            bool target_lost = false;
            bool prempt_requested = false;
            bool reached_location = move_to_location(dock_pose_contact,
                                                     max_action_time_sec,
                                                     error_threshold_xy * 0.75,
                                                     error_threshold_yaw * 0.75,
                                                     true,
                                                     target_lost,
                                                     prempt_requested);

            if (reached_location)
            {
                if (communicate_with_dock_ && !is_lockerdocking_)
                {
                    docking_state = DOCKING_STATE::HARDWARE_START_CHARGING;
                }
                else
                {
                    docking_state = DOCKING_STATE::STOP_APRILTAG;
                }

                break;
            }
            else
            {
                num_retries++;

                if (target_lost)
                {
                    goal_abort_reason_ = "Apriltag target lost";
                    // current_message_ = "Apriltag target lost";
                    print_and_publish_log(goal_abort_reason_);
                    docking_state = DOCKING_STATE::DOCKING_FAILED;
                    break;
                }
                else
                {
                    print_and_publish_log("Going back to move_to_location_2 since all positions not reached");
                    docking_state = DOCKING_STATE::MOVETO_POSE_2;
                    break;
                }
            }

            break;
        }

        case DOCKING_STATE::HARDWARE_START_CHARGING:
        {
            print_and_publish_log(">> entered HARDWARE_START_CHARGING state");

            current_message_ = "In HARDWARE_START_CHARGING state";

            if (hardware_start_charging(1))
            {
                docking_state = DOCKING_STATE::STOP_APRILTAG;
                break;
            }
            else
            {
                goal_abort_reason_ = "Unable to start charging";
                // current_message_ = "Unable to start charging";
                print_and_publish_log(goal_abort_reason_);
                docking_state = DOCKING_STATE::DOCKING_FAILED;
                break;
            }

            break;
        }

        case DOCKING_STATE::HARDWARE_CHECK_CHARGING:
        {
            print_and_publish_log(">> entered HARDWARE_CHECK_CHARGING state");
            current_message_ = "In HARDWARE_CHECK_CHARGING";

            // if charging is broken, abort the charging state
            // if prempt is requested, stop charging and comm, start apriltag
            // undock the robot and prempt the goal

            break;
        }

        case DOCKING_STATE::HARDWARE_STOP_COMM_CHARGING:
        {
            print_and_publish_log(">> entered HARDWARE_STOP_COMM_CHARGING state");

            current_message_ = "In HARDWARE_STOP_COMM_CHARGING";

            if (!is_lockerdocking_ && hardware_start_charging(0) && hardware_start_communication(0))
            {
                docking_state = DOCKING_STATE::UNDOCKING;
                break;
            }
            else if (is_lockerdocking_ && hardware_start_communication(0))
            {
                docking_state = DOCKING_STATE::UNDOCKING;
                break;
            }
            else
            {
                goal_abort_reason_ = "Unable to stop charging / Unable to stop communication with dock";
                // current_message_ = "Unable to stop charging / Unable to stop communication with dock";
                print_and_publish_log(goal_abort_reason_);
                docking_state = DOCKING_STATE::DOCKING_FAILED;
                break;
            }

            break;
        }

        case DOCKING_STATE::UNDOCKING:
        {
            print_and_publish_log(">> entered UNDOCKING state");

            current_message_ = "In UNDOCKING state";

            ros::Duration(1.0).sleep();

            if (!is_lockerdocking_)
                disengage_motors(false, 5);

            ros::Duration(1.0).sleep();

            bool target_lost = false;
            bool prempt_requested = false;
            bool reached_location = false;

            if (!is_lockerdocking_)
                reached_location = move_to_location(undock_pose,
                                                    max_action_time_sec,
                                                    error_threshold_xy,
                                                    error_threshold_yaw,
                                                    !is_lockerdocking_, // Move_only_x make false for lockerundocking
                                                    target_lost,
                                                    prempt_requested);
            else
                reached_location = move_to_location(undock_pose,
                                                    max_action_time_sec,
                                                    error_threshold_xy * 1.75,
                                                    error_threshold_yaw * 1.75,
                                                    !is_lockerdocking_, // Move_only_x make false for lockerundocking
                                                    target_lost,
                                                    prempt_requested);

            if (reached_location)
            {
                docking_state = DOCKING_STATE::STOP_APRILTAG;
                break;
            }
            else
            {
                num_retries++;

                if (target_lost)
                {
                    goal_abort_reason_ = "Apriltag target lost";
                    // current_message_ = "Apriltag target lost";
                    print_and_publish_log(goal_abort_reason_);
                    docking_state = DOCKING_STATE::DOCKING_FAILED;
                    break;
                }
                else
                {
                    print_and_publish_log(">> Staing in undocking state and retrying");
                }
            }

            break;
        }

        // always populate previous_docking_state variable before entering ROTATE_RECOVERY state!
        case DOCKING_STATE::ROTATE_RECOVERY:
        {
            print_and_publish_log(">> entered ROTATE_RECOVERY state");

            current_message_ = "In ROTATE_RECOVERY state";

            ros::Time start_time = ros::Time::now();
            ros::Rate rate(20.0);
            bool found_marker = false;
            bool prempt_requested = false;
            while (ros::Time::now().toSec() - start_time.toSec() < 90.0) // rotate recovery for max 1.5 min
            {
                if (dockAS->isPreemptRequested())
                {
                    publish_velocity(0.0, 0.0, 0.0);
                    prempt_requested = true;
                    break;
                }

                // rotate till marker is found
                if (fabs(ros::Time::now().toSec() - target_detection_time.toSec()) < 0.5)
                {
                    found_marker = true;
                    publish_velocity(0.0, 0.0, 0.0);
                    break;
                }

                // @TODO - determine the direction of rotation based on what marker is visible
                publish_velocity(0.0, 0.0, 0.2);

                rate.sleep();
            }

            if (found_marker)
            {
                docking_state = previous_docking_state; // restart the previous action
                publish_velocity(0.0, 0.0, 0.0);
                break;
            }
            else
            {
                goal_abort_reason_ = "Unable to find marker in rotate recovery";
                // current_message_ = "Unable to find marker in rotate recovery";
                print_and_publish_log(goal_abort_reason_);
                docking_state = DOCKING_STATE::DOCKING_FAILED;
                publish_velocity(0.0, 0.0, 0.0);
                break;
            }

            break;
        }

        case DOCKING_STATE::DOCKING_COMPLETED:
        {
            current_message_ = "In DOCKING_COMPLETED state";

            if (docking_type == DOCKING_TYPE::DOCKING || docking_type == DOCKING_TYPE::MANUALDOCKING)
            {
                if (simulate_)
                {
                    std::string charge_service_name = "/set_charging";
                    bool started_gscam = service_handler(true, charge_service_name);
                }
                else
                    disengage_motors(true, 5);

                ros::Duration(0.5).sleep();
            }

            print_and_publish_log(">> entered DOCKING_COMPLETED state");

            ottonomy_msgs::OttoDockResult result;
            result.id = docking_id;
            result.action_completed = true;
            result.status_message = "Docking succeeded";
            current_message_ = "Docking succeeded";
            dockAS->setSucceeded(result, "Docking succeeded");
            return;
        }

        case DOCKING_STATE::DOCKING_FAILED:
        {
            current_message_ = "In DOCKING_FAILED state";
            print_and_publish_log(">> entered DOCKING_FAILED state");

            ottonomy_msgs::OttoDockResult result;
            result.id = docking_id;
            result.action_completed = false;
            result.status_message = goal_abort_reason_;
            current_message_ = goal_abort_reason_;
            dockAS->setAborted(result, goal_abort_reason_);
            return;
        }

        case DOCKING_STATE::DOCKING_PREMPTED:
        {
            print_and_publish_log(">> entered DOCKING_PREMPTED state");

            current_message_ = "DOCKING_PREMPTED";

            ottonomy_msgs::OttoDockResult result;
            result.id = docking_id;
            result.action_completed = false;
            result.status_message = "Docking prempted";
            dockAS->setPreempted(result, "Docking prempted");
            return;
        }

        default:
        {
            print_and_publish_log("Docking invalid state, aborting");
            current_message_ = "Docking invalid state, aborting";

            ottonomy_msgs::OttoDockResult result;
            result.id = docking_id;
            result.action_completed = false;
            result.status_message = "Docking invalid state, aborting";
            dockAS->setAborted(result, "Docking invalid state, aborting");
            return;
        }
        }
    }

    return;
}

int main(int argc, char **argv)
{
    ros::init(argc, argv, "otto_docking_node");
    ros::NodeHandle n;
    OttoDocking auto_docking(n);

    ros::spin();
    return 0;
}
