# Web framework and server
fastapi==0.104.1
uvicorn==0.24.0
pydantic==2.4.2

# Environment and configuration
python-dotenv==1.0.0
pyyaml==6.0.1

# HTTP clients
httpx==0.25.0
aiohttp==3.9.1
requests==2.31.0

# LLM APIs
openai==1.3.0  # Used for Groq API compatibility
groq==0.4.0    # Groq API client
# google-generativeai==0.1.0rc2 # Or whatever old version is there
google-generativeai==0.1.0rc1 # Updated to reflect available version

# Utilities
colorama>=0.4.6
jinja2==3.1.2
psutil==5.9.5
gitpython>=3.1.0  # For Git repository operations
# tkinter is a built-in Python module, no need to install

# Authentication
python-jose==3.3.0  # For JWT handling
python-multipart==0.0.6  # For form data parsing
