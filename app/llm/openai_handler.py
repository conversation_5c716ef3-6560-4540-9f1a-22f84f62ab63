import os
import yaml
import json
import openai
import logging
import asyncio
import time
from typing import List, Dict, Any
from dotenv import load_dotenv
from app.llm.base import BaseDocGenerator

load_dotenv()
logger = logging.getLogger(__name__)

class OpenAICompatibleGenerator(BaseDocGenerator):
    """
    Documentation generator for OpenAI-compatible APIs (OpenAI, Groq, OpenRouter).
    Includes rate limiting and retry logic.
    """
    def __init__(self, provider: str = None):
        """Initialize the client for the specified provider."""
        self.last_request_time = 0
        self.min_request_interval = 1.0  # Minimum 1 second between requests
        self.max_retries = 3
        try:
            with open('config/config.yaml', 'r') as f:
                config = yaml.safe_load(f)

            # If provider is not specified, determine from environment
            if provider is None:
                if os.getenv("GROQ_API_KEY"):
                    provider = "groq"
                elif os.getenv("OPENAI_API_KEY"):
                    provider = "openai"
                elif os.getenv("OPENROUTER_API_KEY"):
                    provider = "openrouter"
                else:
                    raise ValueError("No API keys found for any supported OpenAI-compatible provider")

            # Provider-specific configuration
            providers = {
                "openai": {
                    "api_key": os.getenv("OPENAI_API_KEY"),
                    "base_url": "https://api.openai.com/v1",
                    "model": config.get('llm', {}).get('models', {}).get('openai', {}).get('model_name', "gpt-4")
                },
                "groq": {
                    "api_key": os.getenv("GROQ_API_KEY"),
                    "base_url": "https://api.groq.com/openai/v1",
                    "model": config.get('llm', {}).get('models', {}).get('groq', {}).get('model_name', "deepseek-r1-distill-llama-70b")
                },
                "openrouter": {
                    "api_key": os.getenv("OPENROUTER_API_KEY"),
                    "base_url": "https://openrouter.ai/api/v1",
                    "model": config.get('llm', {}).get('models', {}).get('openrouter', {}).get('model_name', "meta-ai/llama-3.1-8b-instruct")
                }
            }

            if provider not in providers:
                raise ValueError(f"Unsupported provider: {provider}")

            cfg = providers[provider]
            if not cfg["api_key"]:
                raise ValueError(f"No API key found for {provider}")

            self.client = openai.AsyncOpenAI(api_key=cfg["api_key"], base_url=cfg["base_url"])
            self.model = cfg["model"]
            logger.info(f"Initialized {provider} client with model: {self.model}")

        except Exception as e:
            logger.error(f"Failed to initialize {provider} client: {e}")
            raise

    async def _rate_limit(self):
        """Implement rate limiting between API calls."""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time

        if time_since_last_request < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last_request
            logger.info(f"Rate limiting: waiting {sleep_time:.2f} seconds")
            await asyncio.sleep(sleep_time)

        self.last_request_time = time.time()

    async def generate_inline_comments(self, code: str, language: str) -> List[Dict[str, Any]]:
        """
        Generate inline comments for code blocks with rate limiting and retry logic.
        """
        await self._rate_limit()
        prompt = f"""
        Analyze this {language} code and generate concise, meaningful inline comments for:
        - Function and method purposes
        - Complex logic explanations
        - Important implementation details

        Code:
        {code}

        IMPORTANT: You MUST format your response as a valid JSON object with a "comments" array like this:
        {{
            "comments": [
                {{"line_number": 10, "comment": "Initialize the database connection pool"}},
                {{"line_number": 15, "comment": "Execute the main query with retry logic"}}
            ]
        }}

        Rules:
        - Comments should be clear and concise
        - Focus on explaining 'why' rather than 'what'
        - Only add comments where they add value
        - Don't comment obvious code
        - Your response MUST be valid JSON and nothing else
        """

        for attempt in range(self.max_retries):
            try:
                response = await self.client.chat.completions.create(
                    model=self.model,
                    messages=[
                        {"role": "system", "content": "You are a code documentation expert. Provide clear, concise inline comments in valid JSON format only."},
                        {"role": "user", "content": prompt}
                    ],
                    response_format={"type": "json_object"}
                )

                content = response.choices[0].message.content

                # Try to parse the JSON response
                try:
                    # If the response is a JSON object with a comments array
                    parsed = json.loads(content)
                    if isinstance(parsed, dict) and "comments" in parsed:
                        return parsed["comments"]
                    # If the response is a direct array
                    elif isinstance(parsed, list):
                        return parsed
                    else:
                        # Create a default structure if the JSON doesn't match expected format
                        logger.warning(f"Unexpected JSON structure: {parsed}")
                        return []
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse JSON response: {e}")
                    logger.debug(f"Raw response: {content}")
                    if attempt == self.max_retries - 1:
                        return []
                    continue  # Try again

            except Exception as e:
                logger.warning(f"Attempt {attempt + 1}/{self.max_retries} failed: {e}")
                if attempt == self.max_retries - 1:
                    logger.error(f"All {self.max_retries} attempts failed for comment generation")
                    return []

                # Exponential backoff for retries
                wait_time = 2 ** attempt
                logger.info(f"Retrying in {wait_time} seconds...")
                await asyncio.sleep(wait_time)

        return []