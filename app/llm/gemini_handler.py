import google.generativeai as genai
import os
import yaml
import json
import logging
import asyncio
import time
from functools import partial
from typing import List, Dict, Any
from dotenv import load_dotenv
from app.llm.base import BaseDocGenerator

load_dotenv()
logger = logging.getLogger(__name__)

class GeminiDocGenerator(BaseDocGenerator):
    """
    Documentation generator using Google's Gemini API (adapted for google-generativeai==0.1.0rc1).
    Focuses on inline comments.
    """
    def __init__(self):
        """Initialize the Gemini client."""
        try:
            with open('config/config.yaml', 'r') as f:
                config_data = yaml.safe_load(f)

            api_key = config_data.get('llm', {}).get('models', {}).get('gemini', {}).get('api_key')
            api_key = api_key or os.getenv("GOOGLE_GEMINI_API_KEY")

            # For v0.1.0rc1, use a compatible model name
            self.model_name = config_data.get('llm', {}).get('models', {}).get('gemini', {}).get('model_name', "gemini-pro")

            # Rate limiting
            self.last_request_time = 0
            self.min_request_interval = 2.0  # 2 seconds between requests for Gemini
            self.max_retries = 3

            if not api_key:
                raise ValueError("GOOGLE_GEMINI_API_KEY not found in config or environment variables")

            genai.configure(api_key=api_key)
            logger.info(f"Initialized Gemini with API key. Target model (for generate_text): {self.model_name}")

        except Exception as e:
            logger.error(f"Failed to initialize Gemini client: {e}", exc_info=True)
            raise

    async def _rate_limit(self):
        """Implement rate limiting between API calls."""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time

        if time_since_last_request < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last_request
            logger.info(f"Gemini rate limiting: waiting {sleep_time:.2f} seconds")
            await asyncio.sleep(sleep_time)

        self.last_request_time = time.time()

    async def generate_inline_comments(self, code: str, language: str) -> List[Dict[str, Any]]:
        """
        Generate inline comments for code blocks using google-generativeai==0.1.0rc1.
        Includes rate limiting and retry logic.
        """
        await self._rate_limit()
        prompt = f"""
        Analyze this {language} code and generate meaningful inline comments that explain:
        - Complex logic and algorithms
        - Non-obvious implementation details
        - Important technical decisions

        Code:
        {code}

        IMPORTANT: You MUST format your response as a valid JSON object with a "comments" array like this:
        {{
            "comments": [
                {{"line_number": 10, "comment": "Initialize the database connection pool"}},
                {{"line_number": 15, "comment": "Execute the main query with retry logic"}}
            ]
        }}
        Ensure the entire response is ONLY this JSON object and nothing else.

        Guidelines:
        - Focus on explaining WHY, not WHAT
        - Only comment non-obvious code
        - Keep comments brief and technical
        - Skip obvious or self-documenting code
        """

        prompt_bytes = len(prompt.encode('utf-8'))
        logger.debug(f"Gemini Prompt for {language} code (code length {len(code)}, prompt bytes {prompt_bytes}):\n{prompt}")

        for attempt in range(self.max_retries):
            try:
                # For google-generativeai v0.1.0rc1, try different API methods
                loop = asyncio.get_event_loop()

                try:
                    # Try the newer GenerativeModel approach first
                    model = genai.GenerativeModel(self.model_name)
                    blocking_call = partial(model.generate_content, prompt)
                    response = await loop.run_in_executor(None, blocking_call)
                    response_text = response.text
                except AttributeError:
                    # Fallback to older generate_text method
                    blocking_call = partial(
                        genai.generate_text,
                        model=self.model_name,
                        prompt=prompt
                    )
                    response = await loop.run_in_executor(None, blocking_call)

                    if not response or not hasattr(response, 'result') or not response.result:
                        logger.warning("Received no result or empty result from Gemini generate_text.")
                        if attempt == self.max_retries - 1:
                            return []
                        continue

                    response_text = response.result

                # Try to parse the JSON response
                try:
                    # It's common for LLMs to wrap JSON in ```json ... ```, try to strip it.
                    if response_text.strip().startswith("```json"):
                        response_text = response_text.strip()[7:]
                        if response_text.strip().endswith("```"):
                            response_text = response_text.strip()[:-3]

                    parsed = json.loads(response_text.strip())

                    if isinstance(parsed, dict) and "comments" in parsed:
                        comments_list = parsed["comments"]
                        if isinstance(comments_list, list):
                            valid_comments = []
                            for item in comments_list:
                                if (isinstance(item, dict) and
                                        "line_number" in item and isinstance(item["line_number"], int) and
                                        "comment" in item and isinstance(item["comment"], str)):
                                    valid_comments.append(item)
                                else:
                                    logger.warning(f"Invalid comment item format or type in Gemini response: {item}")
                            return valid_comments
                        else:
                            logger.warning(f"'comments' key in Gemini JSON is not a list: {parsed.get('comments')}")
                            if attempt == self.max_retries - 1:
                                return []
                            continue
                    else:
                        logger.warning(f"Unexpected JSON structure or 'comments' key missing in Gemini response: {parsed}")
                        if attempt == self.max_retries - 1:
                            return []
                        continue

                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse JSON response from Gemini: {e}")
                    logger.debug(f"Raw response text from Gemini for JSON parsing: '{response_text}'")
                    if attempt == self.max_retries - 1:
                        return []
                    continue

            except Exception as e:
                logger.warning(f"Gemini attempt {attempt + 1}/{self.max_retries} failed: {e}")
                if attempt == self.max_retries - 1:
                    logger.error(f"All {self.max_retries} Gemini attempts failed")
                    return []

                # Exponential backoff for retries
                wait_time = 2 ** attempt
                logger.info(f"Retrying Gemini in {wait_time} seconds...")
                await asyncio.sleep(wait_time)

        return []
