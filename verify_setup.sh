#!/bin/bash

# AI Agent Environment Verification Script
# This script verifies that the clean environment is properly set up

echo "🔍 Verifying AI Agent environment setup..."
echo ""

# Check if virtual environment exists
if [ ! -d "venv_clean_isolated" ]; then
    echo "❌ Virtual environment 'venv_clean_isolated' not found!"
    exit 1
else
    echo "✅ Virtual environment found"
fi

# Activate and check packages
source venv_clean_isolated/bin/activate

echo "📦 Checking core packages..."

# Check if requirements.txt exists
if [ ! -f "requirements.txt" ]; then
    echo "❌ requirements.txt not found!"
    exit 1
fi

# Extract package names from requirements.txt (remove version constraints and comments)
required_packages=($(grep -v '^#' requirements.txt | grep -v '^$' | sed 's/[>=<].*//' | sed 's/==.*//' | sort | uniq))
missing_packages=()

echo "📋 Checking packages from requirements.txt..."
for package in "${required_packages[@]}"; do
    if pip show "$package" &> /dev/null; then
        echo "  ✅ $package"
    else
        echo "  ❌ $package (missing)"
        missing_packages+=("$package")
    fi
done

echo ""

# Check for unwanted ROS packages
echo "🔍 Checking for ROS contamination..."
ros_check=$(pip list | grep -E "(ros|gazebo|jsk|rqt|tf|smach|moveit|catkin|cv-bridge|sensor-msgs)" || echo "")

if [ -z "$ros_check" ]; then
    echo "✅ No ROS packages found - environment is clean!"
else
    echo "⚠️  Found ROS packages:"
    echo "$ros_check"
fi

echo ""

# Summary
if [ ${#missing_packages[@]} -eq 0 ] && [ -z "$ros_check" ]; then
    echo "🎉 Environment verification PASSED!"
    echo "🚀 Ready for AI comment generation development!"
else
    echo "⚠️  Environment verification found issues:"
    if [ ${#missing_packages[@]} -gt 0 ]; then
        echo "   - Missing packages: ${missing_packages[*]}"
        echo "   - Run: pip install -r requirements.txt"
    fi
    if [ ! -z "$ros_check" ]; then
        echo "   - ROS packages detected (environment not clean)"
    fi
fi

deactivate
