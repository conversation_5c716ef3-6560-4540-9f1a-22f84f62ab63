#!/usr/bin/env python3
"""
Test script to verify LLM fixes for rate limiting and Gemini API issues.
"""

import asyncio
import os
import sys
from dotenv import load_dotenv

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.llm import get_llm_generator

# Load environment variables
load_dotenv()

# Simple test code for comment generation
TEST_CODE = '''
def fibonacci(n):
    if n <= 1:
        return n
    else:
        return fibonacci(n-1) + fibonacci(n-2)

def main():
    result = fibonacci(10)
    print(f"Fibonacci of 10 is: {result}")

if __name__ == "__main__":
    main()
'''

async def test_provider(provider_name):
    """Test a specific LLM provider."""
    print(f"\n🧪 Testing {provider_name.upper()} provider...")
    
    try:
        # Get the LLM generator
        generator = get_llm_generator(provider_name)
        print(f"✅ Successfully initialized {provider_name} generator")
        
        # Test comment generation
        print(f"🔄 Generating comments with {provider_name}...")
        comments = await generator.generate_inline_comments(TEST_CODE, "python")
        
        if comments:
            print(f"✅ Successfully generated {len(comments)} comments with {provider_name}")
            for comment in comments[:3]:  # Show first 3 comments
                print(f"   Line {comment.get('line_number', 'N/A')}: {comment.get('comment', 'N/A')}")
            if len(comments) > 3:
                print(f"   ... and {len(comments) - 3} more comments")
        else:
            print(f"⚠️  No comments generated with {provider_name}")
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing {provider_name}: {e}")
        return False

async def main():
    """Main test function."""
    print("🚀 Testing LLM Rate Limiting and Gemini API Fixes")
    print("=" * 50)
    
    # Check available providers
    available_providers = []
    
    if os.getenv("GROQ_API_KEY"):
        available_providers.append("groq")
    if os.getenv("OPENAI_API_KEY"):
        available_providers.append("openai")
    if os.getenv("GOOGLE_GEMINI_API_KEY"):
        available_providers.append("gemini")
    
    if not available_providers:
        print("❌ No API keys found! Please set at least one of:")
        print("   - GROQ_API_KEY")
        print("   - OPENAI_API_KEY") 
        print("   - GOOGLE_GEMINI_API_KEY")
        return
    
    print(f"📋 Available providers: {', '.join(available_providers)}")
    
    # Test each available provider
    results = {}
    for provider in available_providers:
        results[provider] = await test_provider(provider)
        
        # Add delay between providers to test rate limiting
        if len(available_providers) > 1:
            print("⏱️  Waiting 3 seconds before next provider...")
            await asyncio.sleep(3)
    
    # Summary
    print("\n📊 Test Results Summary:")
    print("=" * 30)
    for provider, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{provider.upper()}: {status}")
    
    successful_providers = [p for p, success in results.items() if success]
    if successful_providers:
        print(f"\n🎉 {len(successful_providers)} provider(s) working correctly!")
        print("✅ Rate limiting and retry logic implemented")
        if "gemini" in successful_providers:
            print("✅ Gemini API compatibility fixed")
    else:
        print("\n⚠️  No providers working. Check your API keys and network connection.")

if __name__ == "__main__":
    asyncio.run(main())
